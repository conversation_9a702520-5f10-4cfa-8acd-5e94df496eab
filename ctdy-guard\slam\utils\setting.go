package utils

import (
	"slam/slog"
	"strings"

	"gopkg.in/ini.v1"
)

var (
	RunMode string

	Host       string
	DomainName string
	Port       string

	DEB_SRC_URL []string

	Type   string
	DataAk string
	DataSk string
	DBHost string
	DBPort string
	DBName string

	KeycloakBaseURL string
	IssuerURL       string
	ClientID        string
	ClientSk        string
	RedirectURL     string

	CasUrlBase     string
	CasClientID    string
	CasSecretKey   string
	CasLogoutCall  string
	CasRedirectURL string

	LoginType string

	KojiAk       string
	KojiSk       string
	KojiOwner    string
	KojiTestAddr string
	Koji236Addr  string
	Koji237Addr  string
	Koji238Addr  string
	Koji239Addr  string
	Koji240Addr  string
	Koji241Addr  string
	Koji242Addr  string
	Koji201Addr  string
	Koji92Addr   string
	Koji85Addr   string
	Koji98Addr   string

	GerritDomain  string
	GerritPort    int
	GerritSSHPort int
	GerritAk      string
	GerritSk      string

	JenkinsPort int
	JenkinsAk   string
	JenkinsSk   string

	CveUrl string

	CeleryIp   string
	CeleryPort int
	CeleryDish int
	CelerySk   string

	EmailSendType string
	EmailServer   string
	EmailIp       string
	EmailPort     int
	EmailSender   string
	EmailUsername string
	EmailSecret   string

	LXToken     string
	SecretGroup string
	SecretApp   string
	Appid       string
	AgentUrl    string

	ZentaoUrl      string
	ZentaoUsername string
	ZentaoPassword string
	ZentaoAddr     string

	ReposCenterUrl string // 中心仓库地址
	SubmitterId    int    // 提交人id
	CheckerId      int    // 审核人id
	ProxyBaseUrl   string // 代理地址
	SavePath       string // 保存路径
	AutoStockSize  int    // 自动入库大小

	//自动入库定时器配置
	TimerAutoStock       string //自动入库
	TimerAutoStockListen string //监听
	TimerAutoStockFlush  string //刷新Sha256
)

const (
	cfgpath = "../conf/config.ini"
)

func init() {
	file, err := ini.Load(cfgpath)
	if err != nil {
		slog.Error.Println("配置文件读取失败，请检查配置文件！")
		panic("配置文件读取失败，请检查配置文件！")
	}
	RunMode = LoadApp(file)
	LoadServer(file)
	DEB_SRC_URL = LoadDeb(file)
	LoadData(file)
	LoadOidc(file)
	LoadCas(file)
	LoadLogin(file)
	LoadKoji(file)
	LoadGerrit(file)
	LoadJenkins(file)
	LoadCve(file)
	LoadCelery(file)
	LoadLanxin(file)
	LoadEmail(file)
	LoadZendao(file)
	LoadAutostock(file)
	LoadTimer(file)
}

func LoadApp(file *ini.File) string {
	return file.Section("").Key("RUN_MODE").MustString("dev")
}

func LoadServer(file *ini.File) {
	Host = file.Section("server").Key("HOST_IP").MustString("*************")
	DomainName = file.Section("server").Key("HOST_DOMAIN_NAME").MustString("server.kylinos.cn")
	Port = file.Section("server").Key("PORT").MustString("1125")
}

func LoadDeb(file *ini.File) []string {
	debsrcs := file.Section("deb").Key("SRCURL").MustString("")
	debsrcs = strings.TrimSpace(debsrcs)
	if debsrcs == "" {
		return nil
	}
	return strings.Split(debsrcs, ",")
}

func LoadData(file *ini.File) {
	Type = file.Section("database").Key("TYPE").Value()
	DBName = file.Section("database").Key("NAME").Value()
	DBHost = file.Section("database").Key("DATABASE_HOST").Value()
	DataAk = file.Section("database").Key("DATA_AK").Value()
	DataskRaw := file.Section("database").Key("DATA_SK").Value()
	DataSk = Base64ToStr(DataskRaw)
	DBPort = file.Section("database").Key("DATABASE_PORT").MustString("5432")
}

func LoadOidc(file *ini.File) {
	KeycloakBaseURL = file.Section("oidc").Key("KEYCLOAK_BASEURL").Value()
	IssuerURL = file.Section("oidc").Key("ISSUER_URL").Value()
	ClientID = file.Section("oidc").Key("CLINT_ID").Value()
	ClientskRaw := file.Section("oidc").Key("CLIENT_SK").Value()
	ClientSk = Base64ToStr(ClientskRaw)
	RedirectURL = file.Section("oidc").Key("REDIRECT_URL").Value()
}

func LoadCas(file *ini.File) {
	CasUrlBase = file.Section("cas").Key("URL_BASE").Value()
	CasClientID = file.Section("cas").Key("CLIENT_ID").Value()
	CasSecretKey = file.Section("cas").Key("SECRET_KEY").Value()
	CasLogoutCall = file.Section("cas").Key("LOGOUT_CALL").Value()
	CasRedirectURL = file.Section("cas").Key("REDIRECT_URL").Value()
}

func LoadLogin(file *ini.File) {
	LoginType = file.Section("login").Key("LOGIN_TYPE").Value()
}

func LoadKoji(file *ini.File) {
	KojiAk = file.Section("koji").Key("KOJI_AK").Value()
	KojiOwner = file.Section("koji").Key("KOJI_OWNER").Value()
	KojiskRaw := file.Section("koji").Key("KOJI_SK").Value()
	KojiSk = Base64ToStr(KojiskRaw)
	KojiTestAddr = file.Section("koji").Key("KOJI_TEST_ADDR").Value()
	Koji236Addr = file.Section("koji").Key("KOJI_236_ADDR").Value()
	Koji237Addr = file.Section("koji").Key("KOJI_237_ADDR").Value()
	Koji238Addr = file.Section("koji").Key("KOJI_238_ADDR").Value()
	Koji239Addr = file.Section("koji").Key("KOJI_239_ADDR").Value()
	Koji240Addr = file.Section("koji").Key("KOJI_240_ADDR").Value()
	Koji241Addr = file.Section("koji").Key("KOJI_241_ADDR").Value()
	Koji242Addr = file.Section("koji").Key("KOJI_242_ADDR").Value()
	Koji201Addr = file.Section("koji").Key("KOJI_GFB_201_ADDR").Value()
	Koji92Addr = file.Section("koji").Key("KOJI_GFB_92_ADDR").Value()
	Koji85Addr = file.Section("koji").Key("KOJI_GFB_85_ADDR").Value()
	Koji98Addr = file.Section("koji").Key("KOJI_GFB_98_ADDR").Value()
}

func LoadGerrit(file *ini.File) {
	GerritDomain = file.Section("gerrit").Key("GERRIT_DOMAIN").MustString("code.kylinos.cn")
	GerritPort = file.Section("gerrit").Key("GERRIT_PORT").MustInt(8084)
	GerritSSHPort = file.Section("gerrit").Key("GERRIT_SSH_PORT").MustInt(29418)
	GerritAk = file.Section("gerrit").Key("GERRIT_AK").Value()
	GerritskRaw := file.Section("gerrit").Key("GERRIT_SK").Value()
	GerritSk = Base64ToStr(GerritskRaw)
}

func LoadJenkins(file *ini.File) {
	JenkinsPort = file.Section("jenkins").Key("JENKINS_PORT").MustInt(8085)
	JenkinsAk = file.Section("jenkins").Key("JENKINS_AK").Value()
	JenkinsskRaw := file.Section("jenkins").Key("JENKINS_SK").Value()
	JenkinsSk = Base64ToStr(JenkinsskRaw)
}

func LoadCve(file *ini.File) {
	CveUrl = file.Section("cve").Key("CVE_URL").Value()
}

func LoadCelery(file *ini.File) {
	CeleryIp = file.Section("celery").Key("CELERY_IP").Value()
	CeleryPort = file.Section("celery").Key("CELERY_PORT").MustInt(9029)
	CeleryDish = file.Section("celery").Key("CELERY_DISH").MustInt(9)
	CelerySkRaw := file.Section("celery").Key("CELERY_SK").Value()
	CelerySk = Base64ToStr(CelerySkRaw)
}

func LoadLanxin(file *ini.File) {
	LXTokenRaw := file.Section("lanxin").Key("LX_TOKEN").Value()
	LXToken = Base64ToStr(LXTokenRaw)
	SecretGroupRaw := file.Section("lanxin").Key("SECRET_GROUP").Value()
	SecretGroup = Base64ToStr(SecretGroupRaw)
	SecretAppRaw := file.Section("lanxin").Key("SECRET_APP").Value()
	SecretApp = Base64ToStr(SecretAppRaw)
	Appid = file.Section("lanxin").Key("APPID").Value()
	AgentUrl = file.Section("lanxin").Key("AGENT_URL").Value()
}

func LoadEmail(file *ini.File) {
	EmailSendType = file.Section("email").Key("EMAIL_SEND_TYPE").Value()
	EmailServer = file.Section("email").Key("EMAIL_SERVER").Value()
	EmailIp = file.Section("email").Key("EMAIL_IP").Value()
	EmailPort = file.Section("email").Key("EMAIL_PORT").MustInt(25)
	EmailSender = file.Section("email").Key("EMAIL_SENDER").Value()
	EmailUsername = file.Section("email").Key("EMAIL_USERNAME").Value()
	EmailSecretRaw := file.Section("email").Key("EMAIL_SECRET").Value()
	EmailSecret = Base64ToStr(EmailSecretRaw)
}

func LoadZendao(file *ini.File) {
	ZentaoUrl = file.Section("zentao").Key("ZENTAO_URL").Value()
	ZentaoAddr = file.Section("zentao").Key("ZENTAO_ADDR").Value()
	ZentaoUsername = file.Section("zentao").Key("ZENTAO_USERNAME").Value()
	ZentaoPasswordRaw := file.Section("zentao").Key("ZENTAO_PASSWORD").Value()
	ZentaoPassword = Base64ToStr(ZentaoPasswordRaw)
}

func LoadAutostock(file *ini.File) {
	ReposCenterUrl = file.Section("autostock").Key("REPOS_CENTER_URL").Value()
	SubmitterId = file.Section("autostock").Key("SUBMITTER_ID").MustInt()
	CheckerId = file.Section("autostock").Key("CHECKER_ID").MustInt()
	ProxyBaseUrl = file.Section("autostock").Key("PROXY_BASE_URL").Value()
	SavePath = file.Section("autostock").Key("SAVE_PATH").Value()
	AutoStockSize = file.Section("autostock").Key("AUTO_STOCK_SIZE").MustInt(10)
}

func LoadTimer(file *ini.File) {
	TimerAutoStock = file.Section("timer").Key("AUTO_STOCK").Value()
	TimerAutoStockListen = file.Section("timer").Key("AUTO_STOCK_LINTEN").Value()
	TimerAutoStockFlush = file.Section("timer").Key("AUTO_STOCK_FLUSH").Value()
}
