ERROR: 2025/07/31 16:23:23 setting.go:112: 配置文件读取失败，请检查配置文件！
WARNING: 2025/07/31 16:24:29 stock_center.go:631: maintainXflowIdOrder: 未找到xflow_id 2 对应的详细信息
WARNING: 2025/07/31 16:24:29 stock_center.go:631: maintainXflowIdOrder: 未找到xflow_id 1 对应的详细信息
WARNING: 2025/07/31 16:24:29 stock_center.go:631: maintainXflowIdOrder: 未找到xflow_id 2 对应的详细信息
WARNING: 2025/07/31 16:24:29 stock_center.go:631: maintainXflowIdOrder: 未找到xflow_id 3 对应的详细信息
ERROR: 2025/07/31 16:24:29 stock_center.go:621: maintainXflowIdOrder: 无法转换xflow_id 'invalid' 为整数: strconv.Atoi: parsing "invalid": invalid syntax
ERROR: 2025/07/31 16:25:01 stock_center.go:187: CreateStockCenterContextBatch failed: call to Query 'INSERT INTO "slam_stockcentercontext" ("xflow_id","current_flow","hook_status","hook_id","review_list","create_time","update_time","id") VALUES ($1,$2,$3,$4,$5,$6,$7,$8),($9,$10,$11,$12,$13,$14,$15,$16) RETURNING "id"' with args [{Name: Ordinal:1 Value:1} {Name: Ordinal:2 Value:type1} {Name: Ordinal:3 Value:} {Name: Ordinal:4 Value:<nil>} {Name: Ordinal:5 Value:<nil>} {Name: Ordinal:6 Value:2025-07-31 16:25:01.1276008 +0800 CST m=+0.*********} {Name: Ordinal:7 Value:2025-07-31 16:25:01.1276008 +0800 CST m=+0.*********} {Name: Ordinal:8 Value:1} {Name: Ordinal:9 Value:1} {Name: Ordinal:10 Value:type1} {Name: Ordinal:11 Value:} {Name: Ordinal:12 Value:<nil>} {Name: Ordinal:13 Value:<nil>} {Name: Ordinal:14 Value:2025-07-31 16:25:01.1276008 +0800 CST m=+0.*********} {Name: Ordinal:15 Value:2025-07-31 16:25:01.1276008 +0800 CST m=+0.*********} {Name: Ordinal:16 Value:2}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2; call to Rollback transaction, was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:187: CreateStockCenterContextBatch failed: invalid value, should be pointer to struct or slice
ERROR: 2025/07/31 16:25:01 stock_center.go:187: CreateStockCenterContextBatch failed: call to database transaction Begin, was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:123: GetStockCenterListByXflowId failed: 1 call to Query 'SELECT * FROM "slam_stockcenterlist" WHERE xflow_id = $1 ORDER BY "slam_stockcenterlist"."id" LIMIT 1' with args [{Name: Ordinal:1 Value:1}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:123: GetStockCenterListByXflowId failed: 22 call to Query 'SELECT * FROM "slam_stockcenterlist" WHERE xflow_id = $1 ORDER BY "slam_stockcenterlist"."id" LIMIT 1' with args [{Name: Ordinal:1 Value:22}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:133: Create StockCenterList failed: call to database transaction Begin, was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:133: Create StockCenterList failed: call to database transaction Begin, was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:142: CreateStockCenterListBatch failed: call to database transaction Begin, was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:142: CreateStockCenterListBatch failed: invalid value, should be pointer to struct or slice
ERROR: 2025/07/31 16:25:01 stock_center.go:142: CreateStockCenterListBatch failed: call to database transaction Begin, was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:123: GetStockCenterListByXflowId failed: 1 call to Query 'SELECT * FROM "slam_stockcenterlist" WHERE xflow_id = $1 ORDER BY "slam_stockcenterlist"."id" LIMIT 1' with args [{Name: Ordinal:1 Value:1}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:151: UpdateStockCenterList failed, Xflow_id 1 not exist
ERROR: 2025/07/31 16:25:01 stock_center.go:123: GetStockCenterListByXflowId failed: 0 call to Query 'SELECT * FROM "slam_stockcenterlist" WHERE xflow_id = $1 ORDER BY "slam_stockcenterlist"."id" LIMIT 1' with args [{Name: Ordinal:1 Value:0}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:151: UpdateStockCenterList failed, Xflow_id 0 not exist
ERROR: 2025/07/31 16:25:01 stock_center.go:123: GetStockCenterListByXflowId failed: 1 call to Query 'SELECT * FROM "slam_stockcenterlist" WHERE xflow_id = $1 ORDER BY "slam_stockcenterlist"."id" LIMIT 1' with args [{Name: Ordinal:1 Value:1}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:151: UpdateStockCenterList failed, Xflow_id 1 not exist
ERROR: 2025/07/31 16:25:01 stock_center.go:168: GetStockCenterContextByXflowId failed: 1 call to Query 'SELECT * FROM "slam_stockcentercontext" WHERE xflow_id = $1 ORDER BY "slam_stockcentercontext"."id" LIMIT 1' with args [{Name: Ordinal:1 Value:1}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:168: GetStockCenterContextByXflowId failed: 22 call to Query 'SELECT * FROM "slam_stockcentercontext" WHERE xflow_id = $1 ORDER BY "slam_stockcentercontext"."id" LIMIT 1' with args [{Name: Ordinal:1 Value:22}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:178: Create StockCenterContext failed: call to database transaction Begin, was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:178: Create StockCenterContext failed: call to database transaction Begin, was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:168: GetStockCenterContextByXflowId failed: 1 call to Query 'SELECT * FROM "slam_stockcentercontext" WHERE xflow_id = $1 ORDER BY "slam_stockcentercontext"."id" LIMIT 1' with args [{Name: Ordinal:1 Value:1}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:196: UpdateStockCenterContext failed, Xflow_id 1 not exist
ERROR: 2025/07/31 16:25:01 stock_center.go:168: GetStockCenterContextByXflowId failed: 0 call to Query 'SELECT * FROM "slam_stockcentercontext" WHERE xflow_id = $1 ORDER BY "slam_stockcentercontext"."id" LIMIT 1' with args [{Name: Ordinal:1 Value:0}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:196: UpdateStockCenterContext failed, Xflow_id 0 not exist
ERROR: 2025/07/31 16:25:01 stock_center.go:168: GetStockCenterContextByXflowId failed: 1 call to Query 'SELECT * FROM "slam_stockcentercontext" WHERE xflow_id = $1 ORDER BY "slam_stockcentercontext"."id" LIMIT 1' with args [{Name: Ordinal:1 Value:1}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:196: UpdateStockCenterContext failed, Xflow_id 1 not exist
ERROR: 2025/07/31 16:25:01 stock_center.go:216: GetStcListById failed: 1 call to Query 'SELECT * FROM "slam_stcpacklist" WHERE id = $1 ORDER BY "slam_stcpacklist"."id" LIMIT 1' with args [{Name: Ordinal:1 Value:1}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:216: GetStcListById failed: 22 call to Query 'SELECT * FROM "slam_stcpacklist" WHERE id = $1 ORDER BY "slam_stcpacklist"."id" LIMIT 1' with args [{Name: Ordinal:1 Value:22}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:228: GetStcListByXflowId failed: 1 call to Query 'SELECT * FROM "slam_stcpacklist" WHERE xflow_id = $1 ORDER BY id asc' with args [{Name: Ordinal:1 Value:1}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:228: GetStcListByXflowId failed: 22 call to Query 'SELECT * FROM "slam_stcpacklist" WHERE xflow_id = $1 ORDER BY id asc' with args [{Name: Ordinal:1 Value:22}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:238: Create StockCenterPackList failed: call to database transaction Begin, was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:238: Create StockCenterPackList failed: call to database transaction Begin, was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:248: CreateStcPackListBacth failed: call to database transaction Begin, was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:248: CreateStcPackListBacth failed: invalid value, should be pointer to struct or slice
ERROR: 2025/07/31 16:25:01 stock_center.go:248: CreateStcPackListBacth failed: call to database transaction Begin, was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:216: GetStcListById failed: 1 call to Query 'SELECT * FROM "slam_stcpacklist" WHERE id = $1 ORDER BY "slam_stcpacklist"."id" LIMIT 1' with args [{Name: Ordinal:1 Value:1}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:256: UpdateStcPackList failed,id 1 not exist
ERROR: 2025/07/31 16:25:01 stock_center.go:216: GetStcListById failed: 999 call to Query 'SELECT * FROM "slam_stcpacklist" WHERE id = $1 ORDER BY "slam_stcpacklist"."id" LIMIT 1' with args [{Name: Ordinal:1 Value:999}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:256: UpdateStcPackList failed,id 999 not exist
ERROR: 2025/07/31 16:25:01 stock_center.go:216: GetStcListById failed: 1 call to Query 'SELECT * FROM "slam_stcpacklist" WHERE id = $1 ORDER BY "slam_stcpacklist"."id" LIMIT 1' with args [{Name: Ordinal:1 Value:1}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:256: UpdateStcPackList failed,id 1 not exist
ERROR: 2025/07/31 16:25:01 stock_center.go:286: CheckCPackListByPackNameOrigin failed: call to Query 'SELECT slam_kojilist.src_name as pack_name,slam_taglist.prod_id as prod_id,slam_kojilist.ip as koji_ip,slam_stcpacklist.cmd_option as cmd_option,slam_koji_platform.id as koji_id FROM "slam_stcpacklist" left join slam_xflow on slam_stcpacklist.xflow_id = slam_xflow.id left join slam_kojilist on slam_kojilist.xflow_id = slam_xflow.id left join slam_taglist on slam_taglist.id = slam_kojilist.tag left join slam_koji_platform on slam_koji_platform.Addr = slam_kojilist.ip WHERE slam_kojilist.ip in ($1,$2) AND slam_xflow.status in ($3,$4) AND slam_kojilist.type in ($5,$6) AND slam_stcpacklist.pack_name = $7' with args [{Name: Ordinal:1 Value:koji1} {Name: Ordinal:2 Value:koji2} {Name: Ordinal:3 Value:active} {Name: Ordinal:4 Value:finished} {Name: Ordinal:5 Value:build} {Name: Ordinal:6 Value:task} {Name: Ordinal:7 Value:test-package}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:286: CheckCPackListByPackNameOrigin failed: call to Query 'SELECT slam_kojilist.src_name as pack_name,slam_taglist.prod_id as prod_id,slam_kojilist.ip as koji_ip,slam_stcpacklist.cmd_option as cmd_option,slam_koji_platform.id as koji_id FROM "slam_stcpacklist" left join slam_xflow on slam_stcpacklist.xflow_id = slam_xflow.id left join slam_kojilist on slam_kojilist.xflow_id = slam_xflow.id left join slam_taglist on slam_taglist.id = slam_kojilist.tag left join slam_koji_platform on slam_koji_platform.Addr = slam_kojilist.ip WHERE slam_kojilist.ip in ($1) AND slam_xflow.status in ($2,$3) AND slam_kojilist.type in ($4,$5) AND slam_stcpacklist.pack_name = $6' with args [{Name: Ordinal:1 Value:koji1} {Name: Ordinal:2 Value:active} {Name: Ordinal:3 Value:finished} {Name: Ordinal:4 Value:build} {Name: Ordinal:5 Value:task} {Name: Ordinal:6 Value:non-existent-package}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:286: CheckCPackListByPackNameOrigin failed: call to Query 'SELECT slam_kojilist.src_name as pack_name,slam_taglist.prod_id as prod_id,slam_kojilist.ip as koji_ip,slam_stcpacklist.cmd_option as cmd_option,slam_koji_platform.id as koji_id FROM "slam_stcpacklist" left join slam_xflow on slam_stcpacklist.xflow_id = slam_xflow.id left join slam_kojilist on slam_kojilist.xflow_id = slam_xflow.id left join slam_taglist on slam_taglist.id = slam_kojilist.tag left join slam_koji_platform on slam_koji_platform.Addr = slam_kojilist.ip WHERE slam_kojilist.ip in ($1) AND slam_xflow.status in ($2,$3) AND slam_kojilist.type in ($4,$5) AND slam_stcpacklist.pack_name = $6' with args [{Name: Ordinal:1 Value:<nil>} {Name: Ordinal:2 Value:active} {Name: Ordinal:3 Value:finished} {Name: Ordinal:4 Value:build} {Name: Ordinal:5 Value:task} {Name: Ordinal:6 Value:test-package}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:286: CheckCPackListByPackNameOrigin failed: call to Query 'SELECT slam_kojilist.src_name as pack_name,slam_taglist.prod_id as prod_id,slam_kojilist.ip as koji_ip,slam_stcpacklist.cmd_option as cmd_option,slam_koji_platform.id as koji_id FROM "slam_stcpacklist" left join slam_xflow on slam_stcpacklist.xflow_id = slam_xflow.id left join slam_kojilist on slam_kojilist.xflow_id = slam_xflow.id left join slam_taglist on slam_taglist.id = slam_kojilist.tag left join slam_koji_platform on slam_koji_platform.Addr = slam_kojilist.ip WHERE slam_kojilist.ip in ($1) AND slam_xflow.status in ($2,$3) AND slam_kojilist.type in ($4,$5) AND slam_stcpacklist.pack_name = $6' with args [{Name: Ordinal:1 Value:koji1} {Name: Ordinal:2 Value:active} {Name: Ordinal:3 Value:finished} {Name: Ordinal:4 Value:build} {Name: Ordinal:5 Value:task} {Name: Ordinal:6 Value:test-package}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:303: FindStockCenterPackInXflow failed: call to Query 'SELECT count(*) FROM "slam_xflow" left join StockCenterContext on StockCenterContext.xflow_id = slam_xflow.id WHERE StockCenterContext.pack_name = $1 AND (slam_xflow.status = $2 OR slam_xflow.status = $3)' with args [{Name: Ordinal:1 Value:test} {Name: Ordinal:2 Value:active} {Name: Ordinal:3 Value:finished}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:286: CheckCPackListByPackNameOrigin failed: call to Query 'SELECT slam_kojilist.src_name as pack_name,slam_taglist.prod_id as prod_id,slam_kojilist.ip as koji_ip,slam_stcpacklist.cmd_option as cmd_option,slam_koji_platform.id as koji_id FROM "slam_stcpacklist" left join slam_xflow on slam_stcpacklist.xflow_id = slam_xflow.id left join slam_kojilist on slam_kojilist.xflow_id = slam_xflow.id left join slam_taglist on slam_taglist.id = slam_kojilist.tag left join slam_koji_platform on slam_koji_platform.Addr = slam_kojilist.ip WHERE slam_kojilist.ip in ($1) AND slam_xflow.status in ($2,$3) AND slam_kojilist.type in ($4,$5) AND slam_stcpacklist.pack_name = $6' with args [{Name: Ordinal:1 Value:koji1} {Name: Ordinal:2 Value:active} {Name: Ordinal:3 Value:finished} {Name: Ordinal:4 Value:build} {Name: Ordinal:5 Value:task} {Name: Ordinal:6 Value:non-existent-package}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:332: SearchSCPacksBaseDev failed: call to Query 'SELECT slam_stcpacklist.pack_name as pack_name,slam_stcpacklist.xflow_id as xflow_id FROM "slam_stcpacklist" left join slam_xflow on slam_stcpacklist.xflow_id = slam_xflow.id WHERE slam_stcpacklist.developer = $1 AND slam_stcpacklist.prod_release = $2 AND slam_stcpacklist.bizsoft_form = $3 AND slam_stcpacklist.bizsoft_type = $4 AND slam_xflow.status in ($5,$6) AND slam_stcpacklist.pack_name ilike $7' with args [{Name: Ordinal:1 Value:developer} {Name: Ordinal:2 Value:prodRelease} {Name: Ordinal:3 Value:bizsoft_form} {Name: Ordinal:4 Value:bizsoft_type} {Name: Ordinal:5 Value:active} {Name: Ordinal:6 Value:finished} {Name: Ordinal:7 Value:%pack1%}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:332: SearchSCPacksBaseDev failed: call to Query 'SELECT slam_stcpacklist.pack_name as pack_name,slam_stcpacklist.xflow_id as xflow_id FROM "slam_stcpacklist" left join slam_xflow on slam_stcpacklist.xflow_id = slam_xflow.id WHERE slam_stcpacklist.developer = $1 AND slam_stcpacklist.prod_release = $2 AND slam_stcpacklist.bizsoft_form = $3 AND slam_stcpacklist.bizsoft_type = $4 AND slam_xflow.status in ($5,$6) AND slam_stcpacklist.pack_name ilike $7' with args [{Name: Ordinal:1 Value:developer} {Name: Ordinal:2 Value:prodRelease} {Name: Ordinal:3 Value:bizsoft_form} {Name: Ordinal:4 Value:bizsoft_type} {Name: Ordinal:5 Value:active} {Name: Ordinal:6 Value:finished} {Name: Ordinal:7 Value:%developer%}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:332: SearchSCPacksBaseDev failed: call to Query 'SELECT slam_stcpacklist.pack_name as pack_name,slam_stcpacklist.xflow_id as xflow_id FROM "slam_stcpacklist" left join slam_xflow on slam_stcpacklist.xflow_id = slam_xflow.id WHERE slam_stcpacklist.developer = $1 AND slam_stcpacklist.prod_release = $2 AND slam_stcpacklist.bizsoft_form = $3 AND slam_stcpacklist.bizsoft_type = $4 AND slam_xflow.status in ($5,$6)' with args [{Name: Ordinal:1 Value:developer} {Name: Ordinal:2 Value:prodRelease} {Name: Ordinal:3 Value:bizsoft_form} {Name: Ordinal:4 Value:bizsoft_type} {Name: Ordinal:5 Value:active} {Name: Ordinal:6 Value:finished}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:374: GetSCenterSummaryListsOptimized 统计查询失败 - 类型: koji, 错误: call to Query 'SELECT count(*) FROM (SELECT slam_xflow.id FROM "slam_xflow" LEFT JOIN slam_stcpacklist ON slam_stcpacklist.xflow_id = slam_xflow.id WHERE slam_xflow.type IN ($1,$2) AND slam_stcpacklist.developer != '' ORDER BY array_to_string(array_agg(distinct slam_xflow.create_time), ',') desc) as sub' with args [{Name: Ordinal:1 Value:stock_center} {Name: Ordinal:2 Value:stock_biz}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:368: GetSCenterSummaryListsOptimized 统计查询失败 - 类型: arch, 错误: call to Query 'SELECT COUNT(DISTINCT sub.id) FROM (SELECT DISTINCT sub.id FROM (SELECT slam_xflow.id FROM "slam_xflow" LEFT JOIN slam_stcpacklist ON slam_stcpacklist.xflow_id = slam_xflow.id LEFT JOIN slam_products ON slam_products.id = slam_stcpacklist.prod_id WHERE slam_xflow.type IN ($1,$2) AND slam_stcpacklist.pack_name ILIKE $3 AND slam_stcpacklist.developer != '' AND slam_stcpacklist.prod_release = $4 AND slam_products.id = $5 GROUP BY "slam_xflow"."id" ORDER BY array_to_string(array_agg(distinct slam_xflow.create_time), ',') desc) as sub) as sub' with args [{Name: Ordinal:1 Value:stock_center} {Name: Ordinal:2 Value:stock_biz} {Name: Ordinal:3 Value:%test%} {Name: Ordinal:4 Value:arch} {Name: Ordinal:5 Value:active}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:374: GetSCenterSummaryListsOptimized 统计查询失败 - 类型: 5, 错误: call to Query 'SELECT count(*) FROM (SELECT slam_xflow.id FROM "slam_xflow" LEFT JOIN slam_stcpacklist ON slam_stcpacklist.xflow_id = slam_xflow.id WHERE slam_xflow.type IN ($1,$2) AND slam_stcpacklist.pack_name ILIKE $3 AND slam_stcpacklist.bizsoft_form != '' ORDER BY array_to_string(array_agg(distinct slam_xflow.create_time), ',') desc) as sub' with args [{Name: Ordinal:1 Value:stock_center} {Name: Ordinal:2 Value:stock_biz} {Name: Ordinal:3 Value:%key%}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:662: call to Query 'SELECT "stock_type" FROM "slam_stockcenterlist" WHERE xflow_id=$1' with args [{Name: Ordinal:1 Value:18464}], was not expected, next expectation is: ExpectedExec => expecting Exec or ExecContext which:
  - matches sql: 'INSERT INTO "slam_stockcentercontext"'
  - is with arguments:
    0 - {}
    1 - {}
    2 - {}
    3 - {}
    4 - {}
  - should return Result having:
      LastInsertId: 1
      RowsAffected: 2
INFO: 2025/07/31 16:25:01 stock_center.go:606: getDetailsByXflowIds 成功获取 2 条记录，输入ID数量: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:374: GetSCenterSummaryListsOptimized 统计查询失败 - 类型: 3, 错误: 数据库连接失败
ERROR: 2025/07/31 16:25:01 stock_center.go:381: GetSCenterSummaryListsOptimized ID查询失败 - 类型: 3, 页大小: 10, 页索引: 1, 错误: 查询失败
INFO: 2025/07/31 16:25:01 stock_center.go:606: getDetailsByXflowIds 成功获取 2 条记录，输入ID数量: 2
WARNING: 2025/07/31 16:25:01 stock_center.go:566: getDetailsByXflowIds: 空的xflow_id列表
ERROR: 2025/07/31 16:25:01 stock_center.go:599: getDetailsByXflowIds 查询失败 - ID数量: 2, 错误: 数据库查询失败
ERROR: 2025/07/31 16:25:01 stock_center.go:599: getDetailsByXflowIds 查询失败 - ID数量: 1500, 错误: Query 'SELECT 
			string_agg(DISTINCT slam_stcpacklist.pack_name, ',') as src_name,
			slam_xflow.id::text as xflow_id,
			string_agg(DISTINCT slam_stockcenterlist.id::text, ',') as stock_id,
			string_agg(DISTINCT slam_stockcentercontext.id::text, ',') as cont_id,
			string_agg(DISTINCT slam_products.id::text, ',') as product,
			string_agg(DISTINCT slam_kojilist.id::text, ',') as koji_id,
			string_agg(DISTINCT slam_stockcentercontext.current_flow, ',') as current_flow,
			string_agg(DISTINCT slam_stockcenterlist.stock_type, ',') as stock_type,
			string_agg(DISTINCT "user".nickname, ',') as user_name,
			string_agg(DISTINCT slam_xflow.user_id::text, ',') as user_id,
			string_agg(DISTINCT slam_xflow.status, ',') as xflow_status,
			string_agg(DISTINCT slam_xflow.create_time::text, ',') as create_time
		 FROM "slam_xflow" LEFT JOIN slam_stockcentercontext ON slam_stockcentercontext.xflow_id = slam_xflow.id LEFT JOIN slam_stockcenterlist ON slam_stockcenterlist.xflow_id = slam_xflow.id LEFT JOIN slam_stcpacklist ON slam_stcpacklist.xflow_id = slam_xflow.id LEFT JOIN slam_kojilist ON slam_kojilist.xflow_id = slam_xflow.id LEFT JOIN "user" ON "user".id = slam_xflow.user_id LEFT JOIN slam_products ON slam_products.id = slam_stcpacklist.prod_id WHERE slam_xflow.id IN ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17,$18,$19,$20,$21,$22,$23,$24,$25,$26,$27,$28,$29,$30,$31,$32,$33,$34,$35,$36,$37,$38,$39,$40,$41,$42,$43,$44,$45,$46,$47,$48,$49,$50,$51,$52,$53,$54,$55,$56,$57,$58,$59,$60,$61,$62,$63,$64,$65,$66,$67,$68,$69,$70,$71,$72,$73,$74,$75,$76,$77,$78,$79,$80,$81,$82,$83,$84,$85,$86,$87,$88,$89,$90,$91,$92,$93,$94,$95,$96,$97,$98,$99,$100,$101,$102,$103,$104,$105,$106,$107,$108,$109,$110,$111,$112,$113,$114,$115,$116,$117,$118,$119,$120,$121,$122,$123,$124,$125,$126,$127,$128,$129,$130,$131,$132,$133,$134,$135,$136,$137,$138,$139,$140,$141,$142,$143,$144,$145,$146,$147,$148,$149,$150,$151,$152,$153,$154,$155,$156,$157,$158,$159,$160,$161,$162,$163,$164,$165,$166,$167,$168,$169,$170,$171,$172,$173,$174,$175,$176,$177,$178,$179,$180,$181,$182,$183,$184,$185,$186,$187,$188,$189,$190,$191,$192,$193,$194,$195,$196,$197,$198,$199,$200,$201,$202,$203,$204,$205,$206,$207,$208,$209,$210,$211,$212,$213,$214,$215,$216,$217,$218,$219,$220,$221,$222,$223,$224,$225,$226,$227,$228,$229,$230,$231,$232,$233,$234,$235,$236,$237,$238,$239,$240,$241,$242,$243,$244,$245,$246,$247,$248,$249,$250,$251,$252,$253,$254,$255,$256,$257,$258,$259,$260,$261,$262,$263,$264,$265,$266,$267,$268,$269,$270,$271,$272,$273,$274,$275,$276,$277,$278,$279,$280,$281,$282,$283,$284,$285,$286,$287,$288,$289,$290,$291,$292,$293,$294,$295,$296,$297,$298,$299,$300,$301,$302,$303,$304,$305,$306,$307,$308,$309,$310,$311,$312,$313,$314,$315,$316,$317,$318,$319,$320,$321,$322,$323,$324,$325,$326,$327,$328,$329,$330,$331,$332,$333,$334,$335,$336,$337,$338,$339,$340,$341,$342,$343,$344,$345,$346,$347,$348,$349,$350,$351,$352,$353,$354,$355,$356,$357,$358,$359,$360,$361,$362,$363,$364,$365,$366,$367,$368,$369,$370,$371,$372,$373,$374,$375,$376,$377,$378,$379,$380,$381,$382,$383,$384,$385,$386,$387,$388,$389,$390,$391,$392,$393,$394,$395,$396,$397,$398,$399,$400,$401,$402,$403,$404,$405,$406,$407,$408,$409,$410,$411,$412,$413,$414,$415,$416,$417,$418,$419,$420,$421,$422,$423,$424,$425,$426,$427,$428,$429,$430,$431,$432,$433,$434,$435,$436,$437,$438,$439,$440,$441,$442,$443,$444,$445,$446,$447,$448,$449,$450,$451,$452,$453,$454,$455,$456,$457,$458,$459,$460,$461,$462,$463,$464,$465,$466,$467,$468,$469,$470,$471,$472,$473,$474,$475,$476,$477,$478,$479,$480,$481,$482,$483,$484,$485,$486,$487,$488,$489,$490,$491,$492,$493,$494,$495,$496,$497,$498,$499,$500,$501,$502,$503,$504,$505,$506,$507,$508,$509,$510,$511,$512,$513,$514,$515,$516,$517,$518,$519,$520,$521,$522,$523,$524,$525,$526,$527,$528,$529,$530,$531,$532,$533,$534,$535,$536,$537,$538,$539,$540,$541,$542,$543,$544,$545,$546,$547,$548,$549,$550,$551,$552,$553,$554,$555,$556,$557,$558,$559,$560,$561,$562,$563,$564,$565,$566,$567,$568,$569,$570,$571,$572,$573,$574,$575,$576,$577,$578,$579,$580,$581,$582,$583,$584,$585,$586,$587,$588,$589,$590,$591,$592,$593,$594,$595,$596,$597,$598,$599,$600,$601,$602,$603,$604,$605,$606,$607,$608,$609,$610,$611,$612,$613,$614,$615,$616,$617,$618,$619,$620,$621,$622,$623,$624,$625,$626,$627,$628,$629,$630,$631,$632,$633,$634,$635,$636,$637,$638,$639,$640,$641,$642,$643,$644,$645,$646,$647,$648,$649,$650,$651,$652,$653,$654,$655,$656,$657,$658,$659,$660,$661,$662,$663,$664,$665,$666,$667,$668,$669,$670,$671,$672,$673,$674,$675,$676,$677,$678,$679,$680,$681,$682,$683,$684,$685,$686,$687,$688,$689,$690,$691,$692,$693,$694,$695,$696,$697,$698,$699,$700,$701,$702,$703,$704,$705,$706,$707,$708,$709,$710,$711,$712,$713,$714,$715,$716,$717,$718,$719,$720,$721,$722,$723,$724,$725,$726,$727,$728,$729,$730,$731,$732,$733,$734,$735,$736,$737,$738,$739,$740,$741,$742,$743,$744,$745,$746,$747,$748,$749,$750,$751,$752,$753,$754,$755,$756,$757,$758,$759,$760,$761,$762,$763,$764,$765,$766,$767,$768,$769,$770,$771,$772,$773,$774,$775,$776,$777,$778,$779,$780,$781,$782,$783,$784,$785,$786,$787,$788,$789,$790,$791,$792,$793,$794,$795,$796,$797,$798,$799,$800,$801,$802,$803,$804,$805,$806,$807,$808,$809,$810,$811,$812,$813,$814,$815,$816,$817,$818,$819,$820,$821,$822,$823,$824,$825,$826,$827,$828,$829,$830,$831,$832,$833,$834,$835,$836,$837,$838,$839,$840,$841,$842,$843,$844,$845,$846,$847,$848,$849,$850,$851,$852,$853,$854,$855,$856,$857,$858,$859,$860,$861,$862,$863,$864,$865,$866,$867,$868,$869,$870,$871,$872,$873,$874,$875,$876,$877,$878,$879,$880,$881,$882,$883,$884,$885,$886,$887,$888,$889,$890,$891,$892,$893,$894,$895,$896,$897,$898,$899,$900,$901,$902,$903,$904,$905,$906,$907,$908,$909,$910,$911,$912,$913,$914,$915,$916,$917,$918,$919,$920,$921,$922,$923,$924,$925,$926,$927,$928,$929,$930,$931,$932,$933,$934,$935,$936,$937,$938,$939,$940,$941,$942,$943,$944,$945,$946,$947,$948,$949,$950,$951,$952,$953,$954,$955,$956,$957,$958,$959,$960,$961,$962,$963,$964,$965,$966,$967,$968,$969,$970,$971,$972,$973,$974,$975,$976,$977,$978,$979,$980,$981,$982,$983,$984,$985,$986,$987,$988,$989,$990,$991,$992,$993,$994,$995,$996,$997,$998,$999,$1000,$1001,$1002,$1003,$1004,$1005,$1006,$1007,$1008,$1009,$1010,$1011,$1012,$1013,$1014,$1015,$1016,$1017,$1018,$1019,$1020,$1021,$1022,$1023,$1024,$1025,$1026,$1027,$1028,$1029,$1030,$1031,$1032,$1033,$1034,$1035,$1036,$1037,$1038,$1039,$1040,$1041,$1042,$1043,$1044,$1045,$1046,$1047,$1048,$1049,$1050,$1051,$1052,$1053,$1054,$1055,$1056,$1057,$1058,$1059,$1060,$1061,$1062,$1063,$1064,$1065,$1066,$1067,$1068,$1069,$1070,$1071,$1072,$1073,$1074,$1075,$1076,$1077,$1078,$1079,$1080,$1081,$1082,$1083,$1084,$1085,$1086,$1087,$1088,$1089,$1090,$1091,$1092,$1093,$1094,$1095,$1096,$1097,$1098,$1099,$1100,$1101,$1102,$1103,$1104,$1105,$1106,$1107,$1108,$1109,$1110,$1111,$1112,$1113,$1114,$1115,$1116,$1117,$1118,$1119,$1120,$1121,$1122,$1123,$1124,$1125,$1126,$1127,$1128,$1129,$1130,$1131,$1132,$1133,$1134,$1135,$1136,$1137,$1138,$1139,$1140,$1141,$1142,$1143,$1144,$1145,$1146,$1147,$1148,$1149,$1150,$1151,$1152,$1153,$1154,$1155,$1156,$1157,$1158,$1159,$1160,$1161,$1162,$1163,$1164,$1165,$1166,$1167,$1168,$1169,$1170,$1171,$1172,$1173,$1174,$1175,$1176,$1177,$1178,$1179,$1180,$1181,$1182,$1183,$1184,$1185,$1186,$1187,$1188,$1189,$1190,$1191,$1192,$1193,$1194,$1195,$1196,$1197,$1198,$1199,$1200,$1201,$1202,$1203,$1204,$1205,$1206,$1207,$1208,$1209,$1210,$1211,$1212,$1213,$1214,$1215,$1216,$1217,$1218,$1219,$1220,$1221,$1222,$1223,$1224,$1225,$1226,$1227,$1228,$1229,$1230,$1231,$1232,$1233,$1234,$1235,$1236,$1237,$1238,$1239,$1240,$1241,$1242,$1243,$1244,$1245,$1246,$1247,$1248,$1249,$1250,$1251,$1252,$1253,$1254,$1255,$1256,$1257,$1258,$1259,$1260,$1261,$1262,$1263,$1264,$1265,$1266,$1267,$1268,$1269,$1270,$1271,$1272,$1273,$1274,$1275,$1276,$1277,$1278,$1279,$1280,$1281,$1282,$1283,$1284,$1285,$1286,$1287,$1288,$1289,$1290,$1291,$1292,$1293,$1294,$1295,$1296,$1297,$1298,$1299,$1300,$1301,$1302,$1303,$1304,$1305,$1306,$1307,$1308,$1309,$1310,$1311,$1312,$1313,$1314,$1315,$1316,$1317,$1318,$1319,$1320,$1321,$1322,$1323,$1324,$1325,$1326,$1327,$1328,$1329,$1330,$1331,$1332,$1333,$1334,$1335,$1336,$1337,$1338,$1339,$1340,$1341,$1342,$1343,$1344,$1345,$1346,$1347,$1348,$1349,$1350,$1351,$1352,$1353,$1354,$1355,$1356,$1357,$1358,$1359,$1360,$1361,$1362,$1363,$1364,$1365,$1366,$1367,$1368,$1369,$1370,$1371,$1372,$1373,$1374,$1375,$1376,$1377,$1378,$1379,$1380,$1381,$1382,$1383,$1384,$1385,$1386,$1387,$1388,$1389,$1390,$1391,$1392,$1393,$1394,$1395,$1396,$1397,$1398,$1399,$1400,$1401,$1402,$1403,$1404,$1405,$1406,$1407,$1408,$1409,$1410,$1411,$1412,$1413,$1414,$1415,$1416,$1417,$1418,$1419,$1420,$1421,$1422,$1423,$1424,$1425,$1426,$1427,$1428,$1429,$1430,$1431,$1432,$1433,$1434,$1435,$1436,$1437,$1438,$1439,$1440,$1441,$1442,$1443,$1444,$1445,$1446,$1447,$1448,$1449,$1450,$1451,$1452,$1453,$1454,$1455,$1456,$1457,$1458,$1459,$1460,$1461,$1462,$1463,$1464,$1465,$1466,$1467,$1468,$1469,$1470,$1471,$1472,$1473,$1474,$1475,$1476,$1477,$1478,$1479,$1480,$1481,$1482,$1483,$1484,$1485,$1486,$1487,$1488,$1489,$1490,$1491,$1492,$1493,$1494,$1495,$1496,$1497,$1498,$1499,$1500) GROUP BY "slam_xflow"."id"', arguments do not match: expected 1000, but got 1500 arguments
INFO: 2025/07/31 16:25:01 stock_center.go:606: getDetailsByXflowIds 成功获取 1 条记录，输入ID数量: 1
INFO: 2025/07/31 16:25:01 stock_center.go:606: getDetailsByXflowIds 成功获取 10 条记录，输入ID数量: 10
INFO: 2025/07/31 16:25:01 stock_center.go:606: getDetailsByXflowIds 成功获取 2 条记录，输入ID数量: 2
ERROR: 2025/07/31 16:25:01 stock_center.go:374: GetSCenterSummaryListsOptimized 统计查询失败 - 类型: 3, 错误: 数据库连接失败
ERROR: 2025/07/31 16:25:01 stock_center.go:381: GetSCenterSummaryListsOptimized ID查询失败 - 类型: 3, 页大小: 10, 页索引: 1, 错误: 查询失败
WARNING: 2025/07/31 16:25:01 stock_center.go:631: maintainXflowIdOrder: 未找到xflow_id 2 对应的详细信息
WARNING: 2025/07/31 16:25:01 stock_center.go:631: maintainXflowIdOrder: 未找到xflow_id 1 对应的详细信息
WARNING: 2025/07/31 16:25:01 stock_center.go:631: maintainXflowIdOrder: 未找到xflow_id 2 对应的详细信息
WARNING: 2025/07/31 16:25:01 stock_center.go:631: maintainXflowIdOrder: 未找到xflow_id 3 对应的详细信息
ERROR: 2025/07/31 16:25:01 stock_center.go:621: maintainXflowIdOrder: 无法转换xflow_id 'invalid' 为整数: strconv.Atoi: parsing "invalid": invalid syntax
INFO: 2025/07/31 16:25:01 stock_center.go:606: getDetailsByXflowIds 成功获取 3 条记录，输入ID数量: 3
INFO: 2025/07/31 16:25:01 stock_center.go:606: getDetailsByXflowIds 成功获取 2 条记录，输入ID数量: 2
WARNING: 2025/07/31 16:25:01 stock_center.go:566: getDetailsByXflowIds: 空的xflow_id列表
ERROR: 2025/07/31 16:25:01 stock_center.go:599: getDetailsByXflowIds 查询失败 - ID数量: 1, 错误: 查询失败
INFO: 2025/07/31 16:25:01 stock_center.go:606: getDetailsByXflowIds 成功获取 1 条记录，输入ID数量: 1
INFO: 2025/07/31 16:25:01 stock_center.go:606: getDetailsByXflowIds 成功获取 3 条记录，输入ID数量: 3
INFO: 2025/07/31 16:25:01 stock_center.go:606: getDetailsByXflowIds 成功获取 1 条记录，输入ID数量: 1
