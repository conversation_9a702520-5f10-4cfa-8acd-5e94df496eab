package model

import (
	"database/sql/driver"
	"fmt"
	"regexp"
	"slam/global"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// 测试常量定义
const (
	mockDBCreateError = "创建模拟数据库失败: %v"
	mockDBOpenError   = "打开GORM数据库失败: %v"
	testDate1         = "2023-01-01"
	testDate2         = "2023-01-02"
)

func TestCreateStockCenterContextBatch(t *testing.T) {
	t.Run("TestGetProductListsretrieval", func(t *testing.T) {

		id := 1
		id2 := 2
		now := time.Now()
		stockCenterList := &[]StockCenterContext{
			{
				Id:           &id,
				Xflow_id:     1,
				Current_flow: "type1",
				CreatedAt:    now,
				UpdatedAt:    now,
			},
			{
				Id:           &id2,
				Xflow_id:     1,
				Current_flow: "type1",
				CreatedAt:    now,
				UpdatedAt:    now,
			},
		}

		// 模拟批量插入操作
		mock.ExpectBegin()
		mock.ExpectExec(`INSERT INTO "slam_stockcentercontext"`).
			WithArgs(
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(),
			).WillReturnResult(sqlmock.NewResult(1, 2))
		mock.ExpectCommit()

		// 执行测试
		result := CreateStockCenterContextBatch(stockCenterList)
		t.Log(result)

	})

	t.Run("empty list", func(t *testing.T) {
		// 测试空列表
		emptyList := &[]StockCenterContext{}

		// 执行测试
		result := CreateStockCenterContextBatch(emptyList)

		t.Log(result)
	})

	t.Run("nil list", func(t *testing.T) {
		// 执行测试
		result := CreateStockCenterContextBatch(nil)

		t.Log(result)
	})

	t.Run("insertion error", func(t *testing.T) {
		// 测试数据
		now := time.Now()
		id := 1
		stockCenterList := &[]StockCenterContext{
			{
				Id:           &id,
				Xflow_id:     1,
				Current_flow: "type1",
				CreatedAt:    now,
				UpdatedAt:    now,
			},
		}

		// 模拟插入操作失败
		mock.ExpectBegin()
		mock.ExpectExec(`INSERT INTO "slam_stockcentercontext"`).
			WithArgs(
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(),
			).WillReturnError(fmt.Errorf("insertion error"))
		mock.ExpectRollback()

		// 执行测试
		result := CreateStockCenterContextBatch(stockCenterList)

		t.Log(result)
	})
}

func TestGetStockCenterListByXflowId(t *testing.T) {

	t.Run(" Successful retrieval", func(t *testing.T) {
		rows := sqlmock.NewRows([]string{"id", "xflow_id", "product_name", "product_short_name", "product_name_en", "cpe_name", "product_name_upstream", "cpe_name_upstream", "product_type", "submit_type", "product_manager", "project_manager", "technical_manager"}).
			AddRow(1, 1, "Product 1", "P1", "Product One", "cpe1", "upstream1", "upcpe1", "Server", "Internal", 1, 2, 3)
		mock.ExpectQuery(`SELECT * FROM "slam_stockcenterlist" WHERE xflow_id = $1 ORDER BY "slam_stockcenterlist"."id" LIMIT 1`).WithArgs(1).WillReturnRows(rows)

		result := GetStockCenterListByXflowId(1)
		t.Log(result)
	})
	t.Run("test package not exist", func(t *testing.T) {
		rows := sqlmock.NewRows([]string{"id", "product_name", "product_short_name", "product_name_en", "cpe_name", "product_name_upstream", "cpe_name_upstream", "product_type", "submit_type", "product_manager", "project_manager", "technical_manager"}).
			AddRow(1, "Product 1", "P1", "Product One", "cpe1", "upstream1", "upcpe1", "Server", "Internal", 1, 2, 3)
		mock.ExpectQuery(`SELECT * FROM "slam_stockcenterlist" WHERE xflow_id = $1 ORDER BY "slam_stockcenterlist"."id" LIMIT 1`).WithArgs(1).WillReturnRows(rows)

		result := GetStockCenterListByXflowId(22)
		t.Log(result)
	})
}

func TestCreateStockCenterList(t *testing.T) {
	t.Run("TestGetProductListsretrieval", func(t *testing.T) {

		id := 1

		now := time.Now()
		testProductList := &StockCenterList{
			Id:              &id,
			Xflow_id:        1,
			Stock_type:      "type1",
			Packs:           []int{1},
			Supplement_info: "active",
			Test_info:       "desc1",
			Appendix:        []string{"appendix1"},
			CreatedAt:       now,
			UpdatedAt:       now,
		}

		mock.ExpectBegin()
		// 准备期望的 SQL 语句和结果
		mock.ExpectExec(`INSERT INTO  "slam_stocktaglist"`).
			WithArgs(
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(),
			).
			WillReturnResult(sqlmock.NewResult(1, 1))
		// 准备期望的事务提交操作
		mock.ExpectCommit()

		// 调用被测试函数
		result := CreateStockCenterList(testProductList)
		t.Log(result)

	})

	t.Run("TestGetProductListsretrieval", func(t *testing.T) {
		// 准备期望的 SQL 语句和结果
		testProductList := &StockCenterList{
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		mock.ExpectExec(`INSERT INTO  "slam_stocktaglist"`).WillReturnResult(sqlmock.NewResult(1, 1))

		result := CreateStockCenterList(testProductList)
		t.Log(result)

	})
}

func TestCreateStockCenterListBatch(t *testing.T) {
	t.Run("TestGetProductListsretrieval", func(t *testing.T) {

		id := 1
		id2 := 2
		now := time.Now()
		stockCenterList := &[]StockCenterList{
			{
				Id:              &id,
				Xflow_id:        1,
				Stock_type:      "type1",
				Packs:           []int{1},
				Supplement_info: "active",
				Test_info:       "desc1",
				Appendix:        []string{"appendix1"},
				CreatedAt:       now,
				UpdatedAt:       now,
			},
			{
				Id:              &id2,
				Xflow_id:        1,
				Stock_type:      "type1",
				Packs:           []int{1},
				Supplement_info: "active",
				Test_info:       "desc1",
				Appendix:        []string{"appendix1"},
				CreatedAt:       now,
				UpdatedAt:       now,
			},
		}

		// 模拟批量插入操作
		mock.ExpectBegin()
		mock.ExpectExec(`INSERT INTO "slam_stockcenterlist"`).
			WithArgs(
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(),
			).WillReturnResult(sqlmock.NewResult(1, 2))
		mock.ExpectCommit()

		// 执行测试
		result := CreateStockCenterListBatch(stockCenterList)
		t.Log(result)

	})

	t.Run("empty list", func(t *testing.T) {
		// 测试空列表
		emptyList := &[]StockCenterList{}

		// 执行测试
		result := CreateStockCenterListBatch(emptyList)

		t.Log(result)
	})

	t.Run("nil list", func(t *testing.T) {
		// 执行测试
		result := CreateStockCenterListBatch(nil)

		t.Log(result)
	})

	t.Run("insertion error", func(t *testing.T) {
		// 测试数据
		now := time.Now()
		id := 1
		stockCenterList := &[]StockCenterList{
			{
				Id:              &id,
				Xflow_id:        1,
				Stock_type:      "type1",
				Packs:           []int{1},
				Supplement_info: "active",
				Test_info:       "desc1",
				Appendix:        []string{"appendix1"},
				CreatedAt:       now,
				UpdatedAt:       now,
			},
		}

		// 模拟插入操作失败
		mock.ExpectBegin()
		mock.ExpectExec(`INSERT INTO "slam_stockcenterlist"`).
			WithArgs(
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(),
			).WillReturnError(fmt.Errorf("insertion error"))
		mock.ExpectRollback()

		// 执行测试
		result := CreateStockCenterListBatch(stockCenterList)

		t.Log(result)
	})
}

func TestUpdateStockCenterList(t *testing.T) {
	t.Run("successful update", func(t *testing.T) {
		// 测试数据
		now := time.Now()
		id := 1
		stockCenterList := &StockCenterList{
			Id:              &id,
			Xflow_id:        1,
			Stock_type:      "type1",
			Supplement_info: "active",
			Test_info:       "desc1",
			Appendix:        []string{"appendix1"},
			CreatedAt:       now,
			UpdatedAt:       now,
		}

		// 模拟查询现有记录
		existingRows := sqlmock.NewRows([]string{
			"id", "Xflow_id", "Stock_type", "Supplement_info", "Test_info",
			"CreatedAt", "UpdatedAt",
		}).AddRow(
			id, 1, "type1", "active",
			"desc1", now, now,
		)

		mock.ExpectQuery(`SELECT * FROM "slam_stockcenterlist" WHERE xflow_id = $1 ORDER BY "slam_stockcenterlist"."id" LIMIT 1`).
			WithArgs(1).WillReturnRows(existingRows)

		// 模拟更新操作
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "slam_stock_center" SET`).
			WithArgs(
				stockCenterList.Xflow_id,
				stockCenterList.Stock_type,
				stockCenterList.Supplement_info,
				stockCenterList.Test_info,
				sqlmock.AnyArg(), // CreatedAt
				sqlmock.AnyArg(), // UpdatedAt
				id,
			).WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// 执行测试
		result := UpdateStockCenterList(stockCenterList)
		t.Log(result)
	})

	t.Run("update error", func(t *testing.T) {
		id := 999
		stockCenterList := &StockCenterList{
			Id: &id,
		}

		// 模拟查询不存在的记录
		mock.ExpectQuery(`SELECT \* FROM "slam_stock_center" WHERE id = \$1`).
			WithArgs(id).
			WillReturnError(gorm.ErrRecordNotFound)

		// 执行测试
		result := UpdateStockCenterList(stockCenterList)
		t.Log(result)
	})

	t.Run("update error", func(t *testing.T) {
		// 测试数据
		now := time.Now()
		id := 1
		stockCenterList := &StockCenterList{
			Id:              &id,
			Xflow_id:        1,
			Stock_type:      "type1",
			Supplement_info: "active",
			Test_info:       "desc1",
			CreatedAt:       now,
			UpdatedAt:       now,
		}

		// 模拟查询现有记录
		existingRows := sqlmock.NewRows([]string{
			"id", "Xflow_id", "Stock_type", "Supplement_info", "Test_info",
			"CreatedAt", "UpdatedAt",
		}).AddRow(
			id, 1, "type1", "active",
			"desc1", now, now,
		)

		mock.ExpectQuery(`SELECT * FROM "slam_stockcenterlist" WHERE xflow_id = $1 ORDER BY "slam_stockcenterlist"."id" LIMIT 1`).
			WithArgs(1).WillReturnRows(existingRows)

		// 模拟更新操作失败
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "slam_stock_center" SET`).
			WithArgs(
				stockCenterList.Xflow_id,
				stockCenterList.Stock_type,
				stockCenterList.Supplement_info,
				stockCenterList.Test_info,
				sqlmock.AnyArg(), // CreatedAt
				sqlmock.AnyArg(), // UpdatedAt
				id,
			).WillReturnError(fmt.Errorf("update error"))
		mock.ExpectRollback()

		// 执行测试
		result := UpdateStockCenterList(stockCenterList)

		t.Log(result)
	})

}

func TestGetStockCenterContextByXflowId(t *testing.T) {

	t.Run(" Successful retrieval", func(t *testing.T) {
		rows := sqlmock.NewRows([]string{"id", "xflow_id", "product_name", "product_short_name", "product_name_en", "cpe_name", "product_name_upstream", "cpe_name_upstream", "product_type", "submit_type", "product_manager", "project_manager", "technical_manager"}).
			AddRow(1, 1, "Product 1", "P1", "Product One", "cpe1", "upstream1", "upcpe1", "Server", "Internal", 1, 2, 3)
		mock.ExpectQuery(`SELECT * FROM "slam_stockcentercontext" WHERE xflow_id = $1 ORDER BY "slam_stockcentercontext"."id" LIMIT 1`).WithArgs(1).WillReturnRows(rows)

		result := GetStockCenterContextByXflowId(1)
		t.Log(result)
	})
	t.Run("test package not exist", func(t *testing.T) {
		rows := sqlmock.NewRows([]string{"id", "product_name", "product_short_name", "product_name_en", "cpe_name", "product_name_upstream", "cpe_name_upstream", "product_type", "submit_type", "product_manager", "project_manager", "technical_manager"}).
			AddRow(1, "Product 1", "P1", "Product One", "cpe1", "upstream1", "upcpe1", "Server", "Internal", 1, 2, 3)
		mock.ExpectQuery(`SELECT * FROM "slam_stockcentercontext" WHERE xflow_id = $1 ORDER BY "slam_stockcentercontext"."id" LIMIT 1`).WithArgs(1).WillReturnRows(rows)

		result := GetStockCenterContextByXflowId(22)
		t.Log(result)
	})

}

func TestCreateStockCenterContext(t *testing.T) {
	t.Run("TestGetProductListsretrieval", func(t *testing.T) {

		id := 1

		now := time.Now()
		testProductList := &StockCenterContext{
			Id:           &id,
			Xflow_id:     1,
			Current_flow: "type1",
			Hook_status:  "active",
			CreatedAt:    now,
			UpdatedAt:    now,
		}

		mock.ExpectBegin()
		// 准备期望的 SQL 语句和结果
		mock.ExpectExec(`INSERT INTO  "slam_stockcentercontext"`).
			WithArgs(
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(), sqlmock.AnyArg(),
			).
			WillReturnResult(sqlmock.NewResult(1, 1))
		// 准备期望的事务提交操作
		mock.ExpectCommit()

		// 调用被测试函数
		result := CreateStockCenterContext(testProductList)
		t.Log(result)

	})

	t.Run("TestGetProductListsretrieval", func(t *testing.T) {
		// 准备期望的 SQL 语句和结果
		testProductList := &StockCenterContext{

			Xflow_id:     1,
			Current_flow: "type1",
			Hook_status:  "active",
		}
		mock.ExpectExec(`INSERT INTO  "slam_stockcentercontext"`).WillReturnResult(sqlmock.NewResult(1, 1))

		result := CreateStockCenterContext(testProductList)
		t.Log(result)

	})
}

func TestUpdateStockCenterContext(t *testing.T) {
	t.Run("successful update", func(t *testing.T) {
		// 测试数据
		now := time.Now()
		id := 1
		stockCenterList := &StockCenterContext{
			Id:           &id,
			Xflow_id:     1,
			Current_flow: "type1",
			Hook_status:  "active",
			CreatedAt:    now,
			UpdatedAt:    now,
		}

		// 模拟查询现有记录
		existingRows := sqlmock.NewRows([]string{
			"id", "Xflow_id", "Current_flow", "Hook_status",
			"CreatedAt", "UpdatedAt",
		}).AddRow(
			id, 1, "type1", "active",
			now, now,
		)

		mock.ExpectQuery(`SELECT * FROM "slam_stockcentercontext" WHERE xflow_id = $1 ORDER BY "slam_stockcentercontext"."id" LIMIT 1`).
			WithArgs(1).WillReturnRows(existingRows)

		// 模拟更新操作
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "slam_stockcentercontext" SET`).
			WithArgs(
				stockCenterList.Xflow_id,
				stockCenterList.Current_flow,
				stockCenterList.Hook_status,
				sqlmock.AnyArg(), // CreatedAt
				sqlmock.AnyArg(), // UpdatedAt
				id,
			).WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// 执行测试
		result := UpdateStockCenterContext(stockCenterList)
		t.Log(result)
	})

	t.Run("update error", func(t *testing.T) {
		id := 999
		stockCenterList := &StockCenterContext{
			Id: &id,
		}

		// 模拟查询不存在的记录
		mock.ExpectQuery(`SELECT * FROM "slam_stockcentercontext" WHERE xflow_id = $1 ORDER BY "slam_stockcentercontext"."id" LIMIT 1`).
			WithArgs(id).
			WillReturnError(gorm.ErrRecordNotFound)

		// 执行测试
		result := UpdateStockCenterContext(stockCenterList)
		t.Log(result)
	})

	t.Run("update error", func(t *testing.T) {
		// 测试数据
		now := time.Now()
		id := 1
		stockCenterList := &StockCenterContext{
			Id:           &id,
			Xflow_id:     1,
			Current_flow: "type1",
			Hook_status:  "active",
			CreatedAt:    now,
			UpdatedAt:    now,
		}

		// 模拟查询现有记录
		existingRows := sqlmock.NewRows([]string{
			"id", "Xflow_id", "Current_flow", "Hook_status",
			"CreatedAt", "UpdatedAt",
		}).AddRow(
			id, 1, "type1", "active",
			now, now,
		)

		mock.ExpectQuery(`SELECT * FROM "slam_stockcentercontext" WHERE xflow_id = $1 ORDER BY "slam_stockcentercontext"."id" LIMIT 1`).
			WithArgs(1).WillReturnRows(existingRows)

		// 模拟更新操作失败
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "slam_stockcentercontext" SET`).
			WithArgs(
				stockCenterList.Xflow_id,
				stockCenterList.Current_flow,
				stockCenterList.Hook_status,
				sqlmock.AnyArg(), // CreatedAt
				sqlmock.AnyArg(), // UpdatedAt
				id,
			).WillReturnError(fmt.Errorf("update error"))
		mock.ExpectRollback()

		// 执行测试
		result := UpdateStockCenterContext(stockCenterList)

		t.Log(result)
	})
}

func TestGetStcListById(t *testing.T) {

	t.Run("bct Successful retrieval", func(t *testing.T) {
		rows := sqlmock.NewRows([]string{"id", "product_name", "product_short_name", "product_name_en", "cpe_name", "product_name_upstream", "cpe_name_upstream", "product_type", "submit_type", "product_manager", "project_manager", "technical_manager"}).
			AddRow(1, "Product 1", "P1", "Product One", "cpe1", "upstream1", "upcpe1", "Server", "Internal", 1, 2, 3)
		mock.ExpectQuery(`SELECT * FROM "slam_stcpacklist" WHERE id = $1 ORDER BY "slam_stcpacklist"."id" LIMIT 1`).WithArgs(1).WillReturnRows(rows)

		result := GetStcListById(1)
		t.Log(result)
	})
	t.Run("bct test package not exist", func(t *testing.T) {
		rows := sqlmock.NewRows([]string{"id", "product_name", "product_short_name", "product_name_en", "cpe_name", "product_name_upstream", "cpe_name_upstream", "product_type", "submit_type", "product_manager", "project_manager", "technical_manager"}).
			AddRow(1, "Product 1", "P1", "Product One", "cpe1", "upstream1", "upcpe1", "Server", "Internal", 1, 2, 3)
		mock.ExpectQuery(`SELECT * FROM "slam_stcpacklist" WHERE id = $1 ORDER BY "slam_stcpacklist"."id" LIMIT 1`).WithArgs(1).WillReturnRows(rows)

		result := GetStcListById(22)
		t.Log(result)
	})
}

func TestGetStcListByXflowId(t *testing.T) {

	t.Run(" Successful retrieval", func(t *testing.T) {
		rows := sqlmock.NewRows([]string{"id", "xflow_id", "product_name", "product_short_name", "product_name_en", "cpe_name", "product_name_upstream", "cpe_name_upstream", "product_type", "submit_type", "product_manager", "project_manager", "technical_manager"}).
			AddRow(1, 1, "Product 1", "P1", "Product One", "cpe1", "upstream1", "upcpe1", "Server", "Internal", 1, 2, 3)
		mock.ExpectQuery(`SELECT * FROM "slam_stcpacklist" WHERE xflow_id = $1 ORDER BY id asc`).WithArgs(1).WillReturnRows(rows)

		result := GetStcListByXflowId(1)
		t.Log(result)
	})
	t.Run("test package not exist", func(t *testing.T) {
		rows := sqlmock.NewRows([]string{"id", "product_name", "product_short_name", "product_name_en", "cpe_name", "product_name_upstream", "cpe_name_upstream", "product_type", "submit_type", "product_manager", "project_manager", "technical_manager"}).
			AddRow(1, "Product 1", "P1", "Product One", "cpe1", "upstream1", "upcpe1", "Server", "Internal", 1, 2, 3)
		mock.ExpectQuery(`SELECT * FROM "slam_stcpacklist" WHERE xflow_id = $1 ORDER BY id asc`).WithArgs(1).WillReturnRows(rows)

		result := GetStcListByXflowId(22)
		t.Log(result)
	})
}

func TestCreateStcPackList(t *testing.T) {
	t.Run("TestCreateStcPackList", func(t *testing.T) {

		id := 1
		now := time.Now()
		testProductList := &StockCenterPackList{
			Id:        &id,
			Xflow_id:  1,
			PackName:  "test",
			Developer: "test",
			CreatedAt: now,
			UpdatedAt: now,
		}

		mock.ExpectBegin()
		// 准备期望的 SQL 语句和结果
		mock.ExpectExec(`INSERT INTO  "slam_stcpacklist"`).
			WithArgs(
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(),
			).
			WillReturnResult(sqlmock.NewResult(1, 1))
		// 准备期望的事务提交操作
		mock.ExpectCommit()

		// 调用被测试函数
		result := CreateStcPackList(testProductList)
		t.Log(result)

	})

	t.Run("TestGetProductListsretrieval", func(t *testing.T) {
		// 准备期望的 SQL 语句和结果
		testProductList := &StockCenterPackList{
			Xflow_id:  1,
			PackName:  "test",
			Developer: "test",
		}
		mock.ExpectExec(`INSERT INTO  "slam_stcpacklist"`).WillReturnResult(sqlmock.NewResult(1, 1))

		result := CreateStcPackList(testProductList)
		t.Log(result)

	})
}

func TestCreateStcPackListBacth(t *testing.T) {
	t.Run("TestGetProductListsretrieval", func(t *testing.T) {

		id := 1
		id2 := 2
		now := time.Now()
		stockCenterList := &[]StockCenterPackList{
			{
				Id:        &id,
				Xflow_id:  1,
				PackName:  "type1",
				Developer: "test",
				CreatedAt: now,
				UpdatedAt: now,
			},
			{
				Id:        &id2,
				Xflow_id:  1,
				PackName:  "type1",
				Developer: "test",
				CreatedAt: now,
				UpdatedAt: now,
			},
		}

		// 模拟批量插入操作
		mock.ExpectBegin()
		mock.ExpectExec(`INSERT INTO "slam_stcpacklist"`).
			WithArgs(
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(),
			).WillReturnResult(sqlmock.NewResult(1, 2))
		mock.ExpectCommit()

		// 执行测试
		result := CreateStcPackListBacth(stockCenterList)
		t.Log(result)

	})

	t.Run("empty list", func(t *testing.T) {
		// 测试空列表
		emptyList := &[]StockCenterPackList{}

		// 执行测试
		result := CreateStcPackListBacth(emptyList)

		t.Log(result)
	})

	t.Run("nil list", func(t *testing.T) {
		// 执行测试
		result := CreateStcPackListBacth(nil)

		t.Log(result)
	})

	t.Run("insertion error", func(t *testing.T) {
		// 测试数据
		now := time.Now()
		id := 1
		stockCenterList := &[]StockCenterPackList{
			{
				Id:        &id,
				Xflow_id:  1,
				PackName:  "type1",
				Developer: "test",
				CreatedAt: now,
				UpdatedAt: now,
			},
		}

		// 模拟插入操作失败
		mock.ExpectBegin()
		mock.ExpectExec(`INSERT INTO "slam_stockcenterlist"`).
			WithArgs(
				sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(), sqlmock.AnyArg(),
				sqlmock.AnyArg(),
			).WillReturnError(fmt.Errorf("insertion error"))
		mock.ExpectRollback()

		result := CreateStcPackListBacth(stockCenterList)

		t.Log(result)
	})
}

func TestUpdateStcPackList(t *testing.T) {
	t.Run("successful update", func(t *testing.T) {
		// 测试数据
		id := 1
		stockCenterList := &StockCenterPackList{
			Id:        &id,
			Xflow_id:  1,
			PackName:  "type1",
			Developer: "active",
		}

		// 模拟查询现有记录
		existingRows := sqlmock.NewRows([]string{
			"id", "Xflow_id", "PackName", "Developer",
		}).AddRow(
			id, 1, "type1", "active",
		)

		mock.ExpectQuery(`SELECT * FROM "slam_stcpacklist" WHERE id = $1 ORDER BY "slam_stcpacklist"."id" LIMIT 1`).
			WithArgs(1).WillReturnRows(existingRows)

		// 模拟更新操作
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "slam_stockcentercontext" SET`).
			WithArgs(
				stockCenterList.Xflow_id,
				stockCenterList.PackName,
				stockCenterList.Developer,
				sqlmock.AnyArg(), // CreatedAt
				sqlmock.AnyArg(), // UpdatedAt
				id,
			).WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		// 执行测试
		result := UpdateStcPackList(stockCenterList)
		t.Log(result)
	})

	t.Run("update error", func(t *testing.T) {
		id := 999
		stockCenterList := &StockCenterPackList{
			Id: &id,
		}

		// 模拟查询不存在的记录
		mock.ExpectQuery(`SELECT * FROM "slam_stcpacklist" WHERE id = $1 ORDER BY "slam_stcpacklist"."id" LIMIT 1`).
			WithArgs(id).
			WillReturnError(gorm.ErrRecordNotFound)

		// 执行测试
		result := UpdateStcPackList(stockCenterList)
		t.Log(result)
	})

	t.Run("update error", func(t *testing.T) {
		// 测试数据
		now := time.Now()
		id := 1
		stockCenterList := &StockCenterPackList{
			Id:        &id,
			Xflow_id:  1,
			PackName:  "type1",
			Developer: "active",
			CreatedAt: now,
			UpdatedAt: now,
		}

		// 模拟查询现有记录
		existingRows := sqlmock.NewRows([]string{
			"id", "Xflow_id", "PackName", "Developer",
			"CreatedAt", "UpdatedAt",
		}).AddRow(
			id, 1, "type1", "active",
			now, now,
		)

		mock.ExpectQuery(`SELECT * FROM "slam_stcpacklist" WHERE id = $1 ORDER BY "slam_stcpacklist"."id" LIMIT 1`).
			WithArgs(1).WillReturnRows(existingRows)

		// 模拟更新操作失败
		mock.ExpectBegin()
		mock.ExpectExec(`UPDATE "slam_stockcentercontext" SET`).
			WithArgs(
				stockCenterList.Xflow_id,
				stockCenterList.PackName,
				stockCenterList.Developer,
				sqlmock.AnyArg(), // CreatedAt
				sqlmock.AnyArg(), // UpdatedAt
				id,
			).WillReturnError(fmt.Errorf("update error"))
		mock.ExpectRollback()

		// 执行测试
		result := UpdateStcPackList(stockCenterList)

		t.Log(result)
	})
}

func TestCheckCPackListByPackNameOrigin(t *testing.T) {

	t.Run("found matching packages", func(t *testing.T) {
		// 测试数据
		packName := "test-package"
		kojiPlatIds := []string{"koji1", "koji2"}
		now := time.Now()

		// 模拟查询结果
		rows := sqlmock.NewRows([]string{
			"stock_id", "stock_name", "stock_type", "stock_status",
			"stock_desc", "tag_id", "tag_name", "tag_type",
			"tag_status", "create_time", "update_time",
			"user_id", "display_type",
		}).AddRow(
			1, "stock1", "type1", "active",
			"desc1", 1, "tag1", "tagtype1",
			"active", now, now,
			1, "normal",
		).AddRow(
			2, "stock2", "type2", "active",
			"desc2", 2, "tag2", "tagtype2",
			"active", now, now,
			1, "normal",
		)

		// 构建预期的查询
		expectedQuery := `SELECT slam_stock_center\.\* FROM "slam_stock_center"` +
			` WHERE stock_name = \$1 AND stock_type = 'koji' AND stock_status = 'active'` +
			` AND tag_status = 'active' AND display_type = 'normal'` +
			` AND stock_desc IN \(\$2, \$3\)`

		mock.ExpectQuery(expectedQuery).
			WithArgs(packName, kojiPlatIds[0], kojiPlatIds[1]).
			WillReturnRows(rows)

		// 执行测试
		exists, results := CheckCPackListByPackNameOrigin(packName, kojiPlatIds)

		t.Log(exists, results)
	})

	t.Run("no matching packages", func(t *testing.T) {
		// 测试数据
		packName := "non-existent-package"
		kojiPlatIds := []string{"koji1"}

		// 构建预期的查询
		expectedQuery := `SELECT slam_stock_center\.\* FROM "slam_stock_center"` +
			` WHERE stock_name = \$1 AND stock_type = 'koji' AND stock_status = 'active'` +
			` AND tag_status = 'active' AND display_type = 'normal'` +
			` AND stock_desc IN \(\$2\)`

		mock.ExpectQuery(expectedQuery).
			WithArgs(packName, kojiPlatIds[0]).
			WillReturnRows(sqlmock.NewRows([]string{}))

		// 执行测试
		exists, results := CheckCPackListByPackNameOrigin(packName, kojiPlatIds)

		t.Log(exists, results)
	})

	t.Run("empty koji platform ids", func(t *testing.T) {
		// 测试数据
		packName := "test-package"
		var kojiPlatIds []string

		// 执行测试
		exists, results := CheckCPackListByPackNameOrigin(packName, kojiPlatIds)

		t.Log(exists, results)
	})

	t.Run("query error", func(t *testing.T) {
		// 测试数据
		packName := "test-package"
		kojiPlatIds := []string{"koji1"}

		// 构建预期的查询
		expectedQuery := `SELECT slam_stock_center\.\* FROM "slam_stock_center"` +
			` WHERE stock_name = \$1 AND stock_type = 'koji' AND stock_status = 'active'` +
			` AND tag_status = 'active' AND display_type = 'normal'` +
			` AND stock_desc IN \(\$2\)`

		mock.ExpectQuery(expectedQuery).
			WithArgs(packName, kojiPlatIds[0]).
			WillReturnError(fmt.Errorf("database error"))

		// 执行测试
		exists, results := CheckCPackListByPackNameOrigin(packName, kojiPlatIds)

		t.Log(exists, results)
	})
}

func TestFindStockCenterPackInXflow(t *testing.T) {

	t.Run("found matching packages", func(t *testing.T) {
		packName := "test"
		now := time.Now()

		// 模拟查询结果
		rows := sqlmock.NewRows([]string{
			"stock_id", "stock_name", "stock_type", "stock_status",
			"stock_desc", "tag_id", "tag_name", "tag_type",
			"tag_status", "create_time", "update_time",
			"user_id", "display_type",
		}).AddRow(
			1, "stock1", "type1", "active",
			"desc1", 1, "tag1", "tagtype1",
			"active", now, now,
			1, "normal",
		).AddRow(
			2, "stock2", "type2", "active",
			"desc2", 2, "tag2", "tagtype2",
			"active", now, now,
			1, "normal",
		)

		// 构建预期的查询
		expectedQuery := `SELECT count(*) FROM "slam_xflow" left join StockCenterContext on StockCenterContext.xflow_id = slam_xflow.id WHERE StockCenterContext.pack_name = $1 AND (slam_xflow.status = $2 OR slam_xflow.status = $3)`

		mock.ExpectQuery(expectedQuery).
			WithArgs(packName, global.XFLOW_STATUS_ACTIVE, global.XFLOW_STATUS_FINISHED).
			WillReturnRows(rows)

		// 执行测试
		results := FindStockCenterPackInXflow(packName)

		t.Log(results)
	})

	t.Run("no matching packages", func(t *testing.T) {
		// 测试数据
		packName := "non-existent-package"
		kojiPlatIds := []string{"koji1"}

		// 构建预期的查询
		expectedQuery := `SELECT count(*) FROM "slam_xflow" left join StockCenterContext on StockCenterContext.xflow_id = slam_xflow.id WHERE StockCenterContext.pack_name = $1 AND (slam_xflow.status = $2 OR slam_xflow.status = $3)`

		mock.ExpectQuery(expectedQuery).
			WithArgs(packName, kojiPlatIds[0]).
			WillReturnRows(sqlmock.NewRows([]string{}))

		// 执行测试
		exists, results := CheckCPackListByPackNameOrigin(packName, kojiPlatIds)

		t.Log(exists, results)
	})
}

func TestSearchSCPacksBaseDev(t *testing.T) {

	t.Run("successful search", func(t *testing.T) {

		rows := sqlmock.NewRows([]string{"pack_name", "xflow_id"}).
			AddRow("pack1", 1).
			AddRow("pack2", 2)

		expectedQuery := "SELECT slam_stcpacklist\\.pack_name as pack_name, slam_stcpacklist\\.xflow_id as xflow_id FROM slam_stcpacklist LEFT JOIN slam_xflow ON slam_stcpacklist\\.xflow_id = slam_xflow\\.id WHERE slam_stcpacklist\\.developer = \\$1 AND slam_stcpacklist\\.prod_release = \\$2 AND slam_stcpacklist\\.bizsoft_form = \\$3 AND slam_stcpacklist\\.bizsoft_type = \\$4 AND slam_xflow\\.status IN \\(\\$5,\\$6\\)"
		mock.ExpectQuery(expectedQuery).
			WithArgs("developer", "prodRelease", "bizsoft_form", "bizsoft_type", "ACTIVE", "FINISHED").
			WillReturnRows(rows)

		// 调用被测试的函数
		success, infos := SearchSCPacksBaseDev("developer", "prodRelease", "bizsoft_form", "bizsoft_type", "pack1")
		t.Log(success, infos)
	})

	t.Run("successful search", func(t *testing.T) {
		expectedQuery := "SELECT slam_stcpacklist\\.pack_name as pack_name, slam_stcpacklist\\.xflow_id as xflow_id FROM slam_stcpacklist LEFT JOIN slam_xflow ON slam_stcpacklist\\.xflow_id = slam_xflow\\.id WHERE slam_stcpacklist\\.developer = \\$1 AND slam_stcpacklist\\.prod_release = \\$2 AND slam_stcpacklist\\.bizsoft_form = \\$3 AND slam_stcpacklist\\.bizsoft_type = \\$4 AND slam_xflow\\.status IN \\(\\$5,\\$6\\)"
		mock.ExpectQuery(expectedQuery).
			WithArgs("developer", "prodRelease", "bizsoft_form", "bizsoft_type", "ACTIVE", "FINISHED").
			WillReturnError(sqlmock.ErrCancelled)

		// 调用被测试的函数
		success, infos := SearchSCPacksBaseDev("developer", "prodRelease", "bizsoft_form", "bizsoft_type", "developer")
		t.Log(success, infos)
	})
	t.Run("Empty Result Case", func(t *testing.T) {

		expectedQuery := "SELECT slam_stcpacklist\\.pack_name as pack_name, slam_stcpacklist\\.xflow_id as xflow_id FROM slam_stcpacklist LEFT JOIN slam_xflow ON slam_stcpacklist\\.xflow_id = slam_xflow\\.id WHERE slam_stcpacklist\\.developer = \\$1 AND slam_stcpacklist\\.prod_release = \\$2 AND slam_stcpacklist\\.bizsoft_form = \\$3 AND slam_stcpacklist\\.bizsoft_type = \\$4 AND slam_xflow\\.status IN \\(\\$5,\\$6\\)"
		mock.ExpectQuery(expectedQuery).
			WithArgs("developer", "prodRelease", "bizsoft_form", "bizsoft_type", "ACTIVE", "FINISHED").
			WillReturnRows(sqlmock.NewRows([]string{"pack_name", "xflow_id"}))

		// 调用被测试的函数
		success, infos := SearchSCPacksBaseDev("developer", "prodRelease", "bizsoft_form", "bizsoft_type", "")
		t.Log(success, infos)

	})
}

func TestGetSCenterSummaryLists(t *testing.T) {
	tests := []struct {
		name      string
		stockType string
		pageSize  int
		pageIndex int
		query     SCenterListQuery
		mockRows  *sqlmock.Rows
		mockCount *sqlmock.Rows
		expected  int
		total     int64
	}{
		{
			name:      "basic koji query",
			stockType: "koji",
			pageSize:  10,
			pageIndex: 1,
			query:     SCenterListQuery{},
			mockRows: sqlmock.NewRows([]string{
				"id", "stock_id", "stock_name", "stock_type",
				"stock_status", "stock_desc", "tag_id", "tag_name",
				"tag_type", "tag_status", "create_time", "update_time",
				"user_name", "display_type",
			}).AddRow(
				1, 1, "stock1", "koji",
				"active", "desc1", 1, "tag1",
				"type1", "active", time.Now(), time.Now(),
				"user1", "normal",
			).AddRow(
				2, 2, "stock2", "koji",
				"active", "desc2", 2, "tag2",
				"type2", "active", time.Now(), time.Now(),
				"user2", "normal",
			),
			mockCount: sqlmock.NewRows([]string{"count"}).AddRow(20),
			expected:  2,
			total:     20,
		},
		{
			name:      "filtered arch query",
			stockType: "arch",
			pageSize:  10,
			pageIndex: 1,
			query: SCenterListQuery{
				Key:         "test",
				ProdRelease: "arch",
				BizSoftForm: "active",
				BizSoftType: "type1",
				Product:     "active",
				SortColumn:  "create_time",
				SortOrder:   "desc",
			},
			mockRows: sqlmock.NewRows([]string{
				"id", "stock_id", "stock_name", "stock_type",
				"stock_status", "stock_desc", "tag_id", "tag_name",
				"tag_type", "tag_status", "create_time", "update_time",
				"user_name", "display_type",
			}).AddRow(
				1, 1, "test_stock", "arch",
				"active", "test_desc", 1, "tag1",
				"type1", "active", time.Now(), time.Now(),
				"user1", "normal",
			),
			mockCount: sqlmock.NewRows([]string{"count"}).AddRow(1),
			expected:  1,
			total:     1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 构建基础查询
			baseQuery := `SELECT slam_stock_center\.\*, "user"\.nickname as user_name FROM "slam_stock_center"` +
				` left join "user" on "user"\.id = slam_stock_center\.user_id`

			// 添加过滤条件
			whereClause := " WHERE stock_type = $1"
			args := []driver.Value{tt.stockType}

			if tt.query.Key != "" {
				whereClause += " AND stock_name ilike $" + fmt.Sprint(len(args)+1)
				args = append(args, "%"+tt.query.Key+"%")
			}

			if tt.query.CurrentFlow != "" {
				whereClause += " AND stock_status = $" + fmt.Sprint(len(args)+1)
				args = append(args, tt.query.CurrentFlow)
			}

			if tt.query.BizSoftType != "" {
				whereClause += " AND tag_type = $" + fmt.Sprint(len(args)+1)
				args = append(args, tt.query.BizSoftType)
			}

			if tt.query.CurrentFlow != "" {
				whereClause += " AND tag_status = $" + fmt.Sprint(len(args)+1)
				args = append(args, tt.query.CurrentFlow)
			}

			baseQuery += whereClause

			// 添加排序
			if tt.query.SortColumn != "" && tt.query.SortOrder != "" {
				baseQuery += fmt.Sprintf(" ORDER BY %s %s", tt.query.SortColumn, tt.query.SortOrder)
			}

			// 设置数据查询预期
			expectQuery := mock.ExpectQuery(regexp.QuoteMeta(baseQuery))
			if len(args) > 0 {
				expectQuery.WithArgs(args...)
			}
			expectQuery.WillReturnRows(tt.mockRows)

			// 设置计数查询预期
			countQuery := `SELECT count\(\*\) FROM \(` + regexp.QuoteMeta(baseQuery) + `\) AS count_query`
			expectCount := mock.ExpectQuery(countQuery)
			if len(args) > 0 {
				expectCount.WithArgs(args...)
			}
			expectCount.WillReturnRows(tt.mockCount)

			// 执行测试
			result := GetSCenterSummaryLists(tt.stockType, tt.pageSize, tt.pageIndex, tt.query)

			t.Log(result)
		})
	}

	t.Run("query error", func(t *testing.T) {
		// 模拟查询错误
		rows := sqlmock.NewRows([]string{"src_name", "xflow_id", "stock_id", "cont_id", "product", "koji_id", "current_flow", "stock_type", "user_name", "user_id", "xflow_status", "create_time"}).
			AddRow("pack1,pack2", 1, "stock1,stock2", "cont1,cont2", "prod1,prod2", "koji1,koji2", "flow1,flow2", "type1,type2", "user1,user2", 1, "status1,status2", "time1,time2")

		expectedQuery := "SELECT array_to_string(array_agg(distinct slam_stcpacklist.pack_name), ',') as src_name, slam_xflow.id as xflow_id, array_to_string(array_agg(distinct slam_stockcenterlist.id), ',') as stock_id, array_to_string(array_agg(distinct slam_stockcentercontext.id), ',') as cont_id, array_to_string(array_agg(distinct slam_products.id), ',') as product, array_to_string(array_agg(distinct slam_kojilist.id), ',')as koji_id, array_to_string(array_agg(distinct slam_stockcentercontext.current_flow), ',') as current_flow, array_to_string(array_agg(distinct slam_stockcenterlist.stock_type), ',') as stock_type, array_to_string(array_agg(distinct \"user\".nickname), ',') as user_name, array_to_string(array_agg(distinct slam_xflow.user_id), ',') as user_id, array_to_string(array_agg(distinct slam_xflow.status), ',') as xflow_status, array_to_string(array_agg(distinct slam_xflow.create_time), ',') as create_time FROM slam_xflow LEFT JOIN slam_stockcentercontext ON slam_stockcentercontext.xflow_id = slam_xflow.id LEFT JOIN slam_stockcenterlist ON slam_stockcenterlist.xflow_id = slam_xflow.id LEFT JOIN slam_stcpacklist ON slam_stcpacklist.xflow_id = slam_xflow.id LEFT JOIN slam_kojilist ON slam_kojilist.xflow_id = slam_xflow.id LEFT JOIN \"user\" ON \"user\".id = slam_xflow.user_id LEFT JOIN slam_products ON slam_products.id = slam_stcpacklist.prod_id WHERE slam_stcpacklist.pack_name LIKE \\$1 AND slam_xflow.\"type\" IN (\\$2,\\$3) GROUP BY " + global.DB_XLOWID
		mock.ExpectQuery(expectedQuery).
			WithArgs("%key%", global.XFLOW_TYPE_STOCK_CENTER, global.XFLOW_TYPE_STOCK_BIZ).
			WillReturnRows(rows)

		// 调用被测试的函数
		bq := SCenterListQuery{Key: "key"}
		result := GetSCenterSummaryLists(global.STOCK_TYPE_BUSINESS, 10, 1, bq)
		t.Log(result)
	})
}

func TestGetStockType(t *testing.T) {
	rows := sqlmock.NewRows([]string{"stock_type"}).
		AddRow("2")
	t.Run("stock", func(t *testing.T) {
		mock.ExpectQuery(`SELECT "stock_type" FROM "slam_stockcenterlist" WHERE xflow_id=$1`).
			WithArgs(18464).
			WillReturnRows(rows)
		_ = GetStockType(18464)
	})
}

// TestGetSCenterSummaryListsOptimized 测试优化后的查询函数
func TestGetSCenterSummaryListsOptimized(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB

	t.Run("正常查询_有数据", func(t *testing.T) {
		// 设置统计查询期望 - 匹配实际的子查询结构
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(2)
		mock.ExpectQuery(`SELECT count\(\*\) FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(countRows)

		// 设置ID查询期望 - 匹配实际的子查询结构
		idRows := sqlmock.NewRows([]string{"id"}).
			AddRow(1).
			AddRow(2)
		mock.ExpectQuery(`SELECT DISTINCT sub\.id FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(idRows)

		// 设置详细信息查询期望 - 更新字段名以匹配新的实现
		detailRows := sqlmock.NewRows([]string{
			"src_name", "xflow_id", "stock_id", "cont_id", "product",
			"koji_id", "current_flow", "stock_type", "user_name",
			"user_id", "xflow_status", "create_time",
		}).
			AddRow("pkg1", "1", "101", "201", "1", "301", "flow1", "type1", "user1", "401", "active", testDate1).
			AddRow("pkg2", "2", "102", "202", "2", "302", "flow2", "type2", "user2", "402", "active", testDate2)
		mock.ExpectQuery(`SELECT.*string_agg.*FROM "slam_xflow"`).
			WithArgs(1, 2).
			WillReturnRows(detailRows)

		// 执行测试
		bq := SCenterListQuery{
			Developer: "test_dev",
			Key:       "test_key",
		}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_CenterS, 10, 1, bq)

		// 验证结果
		if result == nil {
			t.Fatal("结果不应为空")
		}
		resultList := result.List.([]SCenterSummaryList)
		if len(resultList) != 2 {
			t.Errorf("期望2条记录，实际得到%d条", len(resultList))
		}
		if result.Page.Total != 2 {
			t.Errorf("期望总数为2，实际为%d", result.Page.Total)
		}
		if resultList[0].XflowId != "1" {
			t.Errorf("期望第一条记录XflowId为1，实际为%s", resultList[0].XflowId)
		}
	})

	t.Run("正常查询_无数据", func(t *testing.T) {
		// 设置统计查询期望 - 匹配实际的子查询结构
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(0)
		mock.ExpectQuery(`SELECT count\(\*\) FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(countRows)

		// 设置ID查询期望 - 匹配实际的子查询结构
		idRows := sqlmock.NewRows([]string{"id"})
		mock.ExpectQuery(`SELECT DISTINCT sub\.id FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(idRows)

		// 执行测试
		bq := SCenterListQuery{
			Key: "不存在的包",
		}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_CenterS, 10, 1, bq)

		// 验证结果
		if result == nil {
			t.Fatal("结果不应为空")
		}
		resultList := result.List.([]SCenterSummaryList)
		if len(resultList) != 0 {
			t.Errorf("期望0条记录，实际得到%d条", len(resultList))
		}
		if result.Page.Total != 0 {
			t.Errorf("期望总数为0，实际为%d", result.Page.Total)
		}
	})

	t.Run("统计查询失败", func(t *testing.T) {
		// 设置统计查询失败期望 - 匹配实际的子查询结构
		mock.ExpectQuery(`SELECT count\(\*\) FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnError(fmt.Errorf("数据库连接失败"))

		// 执行测试
		bq := SCenterListQuery{}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_CenterS, 10, 1, bq)

		// 验证结果
		if result != nil {
			t.Error("期望结果为空，但实际不为空")
		}
	})

	t.Run("ID查询失败", func(t *testing.T) {
		// 设置统计查询期望 - 匹配实际的子查询结构
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(2)
		mock.ExpectQuery(`SELECT count\(\*\) FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(countRows)

		// 设置ID查询失败期望 - 匹配实际的子查询结构
		mock.ExpectQuery(`SELECT DISTINCT sub\.id FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnError(fmt.Errorf("查询失败"))

		// 执行测试
		bq := SCenterListQuery{}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_CenterS, 10, 1, bq)

		// 验证结果
		if result != nil {
			t.Error("期望结果为空，但实际不为空")
		}
	})
}

// TestBuildOptimizedBaseQuery 测试优化后的基础查询构建函数
// 注释：该函数在当前版本中不存在
/*
func TestBuildOptimizedBaseQuery(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf("创建模拟数据库失败: %v", err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf("打开GORM数据库失败: %v", err)
	}
	db = gormDB

	t.Run("中心仓类型查询", func(t *testing.T) {
		bq := SCenterListQuery{
			Developer:   "test_dev",
			ProdRelease: "test_release",
			Key:         "test_key",
		}

		// 执行函数
		query := buildOptimizedBaseQuery(global.STOCK_TYPE_CenterS, bq)

		// 验证查询对象不为空
		if query == nil {
			t.Error("查询对象不应为空")
		}
	})

	t.Run("业务类型查询", func(t *testing.T) {
		bq := SCenterListQuery{
			BizSoftForm: "test_form",
			BizSoftType: "test_type",
			Key:         "test_key",
		}

		// 执行函数
		query := buildOptimizedBaseQuery(global.STOCK_TYPE_BUSINESS, bq)

		// 验证查询对象不为空
		if query == nil {
			t.Error("查询对象不应为空")
		}
	})

	t.Run("包含所有过滤条件的查询", func(t *testing.T) {
		bq := SCenterListQuery{
			Developer:   "test_dev",
			ProdRelease: "test_release",
			Product:     "test_product",
			StartDate:   "2023-01-01",
			EndDate:     "2023-12-31",
			CurrentFlow: global.STOCK_FLOW_FAILED,
			Key:         "test_key",
			UserId:      123,
			SortColumn:  "create_time",
			SortOrder:   "desc",
		}

		// 执行函数
		query := buildOptimizedBaseQuery(global.STOCK_TYPE_CenterS, bq)

		// 验证查询对象不为空
		if query == nil {
			t.Error("查询对象不应为空")
		}
	})
}
*/

// TestApplySearchConditions 测试搜索条件应用函数
func TestApplySearchConditions(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf("创建模拟数据库失败: %v", err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf("打开GORM数据库失败: %v", err)
	}
	db = gormDB

	t.Run("应用搜索关键字条件", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			Key: "test_package",
		}

		// 执行函数
		result := applySearchConditions(query, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})

	t.Run("空搜索关键字", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			Key: "",
		}

		// 执行函数
		result := applySearchConditions(query, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})
}

// TestApplyStockTypeConditions 测试库存类型条件应用函数
func TestApplyStockTypeConditions(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf("创建模拟数据库失败: %v", err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf("打开GORM数据库失败: %v", err)
	}
	db = gormDB

	t.Run("应用业务类型条件", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			BizSoftForm: "form1",
			BizSoftType: "type1",
		}

		// 执行函数
		result := applyStockTypeConditions(query, global.STOCK_TYPE_BUSINESS, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})

	t.Run("应用中心仓类型条件", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			Developer:   "dev1",
			ProdRelease: "release1",
		}

		// 执行函数
		result := applyStockTypeConditions(query, global.STOCK_TYPE_CenterS, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})
}

// TestApplyCommonFilters 测试通用过滤条件应用函数
// 注释：该函数在当前版本中不存在
/*
func TestApplyCommonFilters(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf("创建模拟数据库失败: %v", err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf("打开GORM数据库失败: %v", err)
	}
	db = gormDB

	t.Run("应用所有通用过滤条件", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			Product:     "prod1",
			StartDate:   "2024-01-01",
			EndDate:     "2024-12-31",
			CurrentFlow: "flow1",
			UserId:      123,
		}

		// 执行函数
		result := applyCommonFilters(query, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})

	t.Run("应用失败流程条件", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			CurrentFlow: global.STOCK_FLOW_FAILED,
		}

		// 执行函数
		result := applyCommonFilters(query, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})

	t.Run("应用提交流程条件", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			CurrentFlow: global.STOCK_FLOW_SUBMIT,
		}

		// 执行函数
		result := applyCommonFilters(query, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})
}
*/

// TestGetDetailsByXflowIds 测试根据xflow_id列表获取详细信息函数
// 注释：该函数在当前版本中不存在
/*
func TestGetDetailsByXflowIds(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("创建模拟数据库失败: %v", err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf("打开GORM数据库失败: %v", err)
	}
	db = gormDB

	t.Run("获取多个ID的详细信息", func(t *testing.T) {
		// 设置详细信息查询期望 - 产品字段现在是ID而不是名称
		detailRows := sqlmock.NewRows([]string{
			"src_name", "xflow_id", "stock_id", "cont_id", "product",
			"koji_id", "current_flow", "stock_type", "user_name",
			"user_id", "xflow_status", "create_time",
		}).
			AddRow("pkg1", "1", "101", "201", "1", "301", "flow1", "type1", "user1", "401", "active", testDate1).
			AddRow("pkg2", "2", "102", "202", "2", "302", "flow2", "type2", "user2", "402", "active", testDate2)
		mock.ExpectQuery(`SELECT.*string_agg.*FROM "slam_xflow"`).
			WithArgs(1, 2).
			WillReturnRows(detailRows)

		// 执行函数
		xflowIds := []int{1, 2}
		bq := SCenterListQuery{}
		results := getDetailsByXflowIds(xflowIds, bq)

		// 验证结果
		if len(results) != 2 {
			t.Errorf("期望2条记录，实际得到%d条", len(results))
		}
		if results[0].XflowId != "1" {
			t.Errorf("期望第一条记录XflowId为1，实际为%s", results[0].XflowId)
		}
		if results[1].XflowId != "2" {
			t.Errorf("期望第二条记录XflowId为2，实际为%s", results[1].XflowId)
		}
	})

	t.Run("空ID列表", func(t *testing.T) {
		// 执行函数
		xflowIds := []int{}
		bq := SCenterListQuery{}
		results := getDetailsByXflowIds(xflowIds, bq)

		// 验证结果
		if len(results) != 0 {
			t.Errorf("期望0条记录，实际得到%d条", len(results))
		}
	})

	t.Run("查询失败", func(t *testing.T) {
		// 设置查询失败期望
		mock.ExpectQuery(`SELECT.*FROM "slam_xflow"`).
			WithArgs(1, 2).
			WillReturnError(fmt.Errorf("数据库查询失败"))

		// 执行函数
		xflowIds := []int{1, 2}
		bq := SCenterListQuery{}
		results := getDetailsByXflowIds(xflowIds, bq)

		// 验证结果
		if len(results) != 0 {
			t.Errorf("期望0条记录，实际得到%d条", len(results))
		}
	})

	t.Run("过大的ID列表", func(t *testing.T) {
		// 创建超过1000个ID的列表
		xflowIds := make([]int, 1500)
		for i := 0; i < 1500; i++ {
			xflowIds[i] = i + 1
		}

		// 设置查询期望（只处理前1000个）
		detailRows := sqlmock.NewRows([]string{
			"src_name", "xflow_id", "stock_id", "cont_id", "product",
			"koji_id", "current_flow", "stock_type", "user_name",
			"user_id", "xflow_status", "create_time",
		}).AddRow("pkg1", "1", "101", "201", "prod1", "301", "flow1", "type1", "user1", "401", "active", "2023-01-01")

		// 期望查询参数为前1000个ID
		expectedArgs := make([]driver.Value, 1000)
		for i := 0; i < 1000; i++ {
			expectedArgs[i] = i + 1
		}

		mock.ExpectQuery(`SELECT.*FROM "slam_xflow"`).
			WithArgs(expectedArgs...).
			WillReturnRows(detailRows)

		// 执行函数
		bq := SCenterListQuery{}
		results := getDetailsByXflowIds(xflowIds, bq)

		// 验证结果
		if len(results) != 1 {
			// t.Errorf("期望1条记录，实际得到%d条", len(results))
		}
	})
}
*/

// TestBusinessTypeConditions 测试业务类型条件应用函数
func TestBusinessTypeConditions(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB

	t.Run("应用业务软件类型条件", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			BizSoftForm: "form1",
			BizSoftType: "type1",
		}

		// 执行函数
		result := applyBusinessTypeConditions(query, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})

	t.Run("空业务软件条件", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{}

		// 执行函数
		result := applyBusinessTypeConditions(query, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})
}

// TestCenterTypeConditions 测试中心仓类型条件应用函数
func TestCenterTypeConditions(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB

	t.Run("应用中心仓开发者条件", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			Developer:   "dev1",
			ProdRelease: "release1",
		}

		// 执行函数
		result := applyCenterTypeConditions(query, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})

	t.Run("空中心仓条件", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{}

		// 执行函数
		result := applyCenterTypeConditions(query, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})
}

// TestDateFilters 测试日期过滤条件应用函数
func TestDateFilters(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB

	t.Run("应用开始和结束日期", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			StartDate: testDate1,
			EndDate:   testDate2,
		}

		// 执行函数
		result := applyDateFilters(query, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})

	t.Run("只有开始日期", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			StartDate: testDate1,
		}

		// 执行函数
		result := applyDateFilters(query, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})

	t.Run("只有结束日期", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			EndDate: testDate2,
		}

		// 执行函数
		result := applyDateFilters(query, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})

	t.Run("无日期条件", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{}

		// 执行函数
		result := applyDateFilters(query, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})
}

// TestCurrentFlowFilters 测试当前流程过滤条件应用函数
func TestCurrentFlowFilters(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB
	/*
		t.Run("应用失败流程过滤", func(t *testing.T) {

				query := db.Table("slam_xflow")
				bq := SCenterListQuery{
					CurrentFlow: global.STOCK_FLOW_FAILED,
				}

				// 执行函数

					result := applyCurrentFlowFilters(query, bq)

					// 验证结果不为空
					if result == nil {
						t.Error("查询结果不应为空")
					}
		})

		t.Run("应用提交流程过滤", func(t *testing.T) {
			query := db.Table("slam_xflow")
			bq := SCenterListQuery{
				CurrentFlow: global.STOCK_FLOW_SUBMIT,
			}

			// 执行函数
			result := applyCurrentFlowFilters(query, bq)

			// 验证结果不为空
			if result == nil {
				t.Error("查询结果不应为空")
			}
		})

		t.Run("应用其他流程过滤", func(t *testing.T) {
			query := db.Table("slam_xflow")
			bq := SCenterListQuery{
				CurrentFlow: "custom_flow",
			}

			// 执行函数
			result := applyCurrentFlowFilters(query, bq)

			// 验证结果不为空
			if result == nil {
				t.Error("查询结果不应为空")
			}
		})

		t.Run("无流程过滤条件", func(t *testing.T) {
			query := db.Table("slam_xflow")
			bq := SCenterListQuery{
				CurrentFlow: "",
			}

			// 执行函数
			result := applyCurrentFlowFilters(query, bq)

			// 验证结果不为空
			if result == nil {
				t.Error("查询结果不应为空")
			}
		})
	*/
}

// TestOptimizedQueryIntegration 测试优化查询的集成测试
func TestOptimizedQueryIntegration(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB

	t.Run("完整的业务类型查询流程", func(t *testing.T) {
		// 设置统计查询期望 - 匹配实际的子查询结构
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(1)
		mock.ExpectQuery(`SELECT count\(\*\) FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(countRows)

		// 设置ID查询期望 - 匹配实际的子查询结构
		idRows := sqlmock.NewRows([]string{"id"}).AddRow(1)
		mock.ExpectQuery(`SELECT DISTINCT sub\.id FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(idRows)

		// 设置详细信息查询期望 - 产品字段现在是ID
		detailRows := sqlmock.NewRows([]string{
			"src_name", "xflow_id", "stock_id", "cont_id", "product",
			"koji_id", "current_flow", "stock_type", "user_name",
			"user_id", "xflow_status", "create_time",
		}).AddRow("bizpkg", "1", "101", "201", "1", "301", "bizflow", "business", "bizuser", "401", "active", testDate1)
		mock.ExpectQuery(`SELECT.*string_agg.*FROM "slam_xflow"`).
			WithArgs(1).
			WillReturnRows(detailRows)

		// 执行测试
		bq := SCenterListQuery{
			BizSoftForm: "form1",
			BizSoftType: "type1",
			Key:         "bizpkg",
			Product:     "bizprod",
			StartDate:   testDate1,
			EndDate:     testDate2,
			CurrentFlow: global.STOCK_FLOW_SUBMIT,
			UserId:      401,
		}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_BUSINESS, 10, 1, bq)

		// 验证结果
		if result == nil {
			t.Fatal("结果不应为空")
		}
		resultList := result.List.([]SCenterSummaryList)
		if len(resultList) != 1 {
			t.Errorf("期望1条记录，实际得到%d条", len(resultList))
		}
		if result.Page.Total != 1 {
			t.Errorf("期望总数为1，实际为%d", result.Page.Total)
		}
		if resultList[0].XflowId != "1" {
			t.Errorf("期望XflowId为1，实际为%s", resultList[0].XflowId)
		}
		if resultList[0].SrcName != "bizpkg" {
			t.Errorf("期望SrcName为bizpkg，实际为%s", resultList[0].SrcName)
		}
	})

	t.Run("分页测试_第二页", func(t *testing.T) {
		// 设置统计查询期望 - 匹配实际的子查询结构
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(25)
		mock.ExpectQuery(`SELECT count\(\*\) FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(countRows)

		// 设置ID查询期望（第二页，每页10条） - 匹配实际的子查询结构
		idRows := sqlmock.NewRows([]string{"id"}).
			AddRow(11).AddRow(12).AddRow(13).AddRow(14).AddRow(15).
			AddRow(16).AddRow(17).AddRow(18).AddRow(19).AddRow(20)
		mock.ExpectQuery(`SELECT DISTINCT sub\.id FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(idRows)

		// 设置详细信息查询期望
		detailRows := sqlmock.NewRows([]string{
			"src_name", "xflow_id", "stock_id", "cont_id", "product",
			"koji_id", "current_flow", "stock_type", "user_name",
			"user_id", "xflow_status", "create_time",
		})
		for i := 11; i <= 20; i++ {
			detailRows.AddRow(fmt.Sprintf("pkg%d", i), fmt.Sprintf("%d", i), fmt.Sprintf("10%d", i),
				fmt.Sprintf("20%d", i), fmt.Sprintf("%d", i), fmt.Sprintf("30%d", i),
				"flow", "center", fmt.Sprintf("user%d", i), fmt.Sprintf("40%d", i), "active", testDate1)
		}
		mock.ExpectQuery(`SELECT.*string_agg.*FROM "slam_xflow"`).
			WithArgs(11, 12, 13, 14, 15, 16, 17, 18, 19, 20).
			WillReturnRows(detailRows)

		// 执行测试（第二页）
		bq := SCenterListQuery{
			Developer: "test_dev",
		}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_CenterS, 10, 2, bq)

		// 验证结果
		if result == nil {
			t.Fatal("结果不应为空")
		}
		resultList := result.List.([]SCenterSummaryList)
		if len(resultList) != 10 {
			t.Errorf("期望10条记录，实际得到%d条", len(resultList))
		}
		if result.Page.Total != 25 {
			t.Errorf("期望总数为25，实际为%d", result.Page.Total)
		}
		if result.Page.Page_index != 2 {
			t.Errorf("期望页索引为2，实际为%d", result.Page.Page_index)
		}
		if result.Page.Page_size != 10 {
			t.Errorf("期望页大小为10，实际为%d", result.Page.Page_size)
		}
	})
}

// TestApplyDistinctWithOrder 测试DISTINCT和ORDER BY兼容性处理函数
// 注释：该函数在当前版本中不存在
/*
func TestApplyDistinctWithOrder(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB

	t.Run("按创建时间排序", func(t *testing.T) {
		query := db.Table("slam_xflow").Select("slam_xflow.id")
		bq := SCenterListQuery{
			SortColumn: "createtime",
			SortOrder:  "desc",
		}

		// 执行函数
		result := applyDistinctWithOrder(query, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})

	t.Run("按ID排序", func(t *testing.T) {
		query := db.Table("slam_xflow").Select("slam_xflow.id")
		bq := SCenterListQuery{
			SortColumn: "id",
			SortOrder:  "asc",
		}

		// 执行函数
		result := applyDistinctWithOrder(query, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})

	t.Run("默认排序", func(t *testing.T) {
		query := db.Table("slam_xflow").Select("slam_xflow.id")
		bq := SCenterListQuery{}

		// 执行函数
		result := applyDistinctWithOrder(query, bq)

		// 验证结果不为空
		if result == nil {
			t.Error("查询结果不应为空")
		}
	})
}
*/

// TestGetSCenterSummaryListsOptimizedNew 测试优化后的主查询函数（新版本）
func TestGetSCenterSummaryListsOptimizedNew2(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB

	t.Run("正常查询_有数据", func(t *testing.T) {
		// 设置统计查询期望 - 由于使用了搜索条件，会生成复杂的子查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(2)
		mock.ExpectQuery(`SELECT count\(\*\) FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(countRows)

		// 设置ID查询期望
		idRows := sqlmock.NewRows([]string{"id"}).
			AddRow(1).
			AddRow(2)
		mock.ExpectQuery(`SELECT DISTINCT sub\.id FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(idRows)

		// 设置详细信息查询期望
		detailRows := sqlmock.NewRows([]string{
			"src_name", "xflow_id", "stock_id", "cont_id", "product",
			"koji_id", "current_flow", "stock_type", "user_name",
			"user_id", "xflow_status", "create_time",
		}).
			AddRow("pkg1", "1", "101", "201", "1", "301", "flow1", "type1", "user1", "401", "active", testDate1).
			AddRow("pkg2", "2", "102", "202", "2", "302", "flow2", "type2", "user2", "402", "active", testDate2)
		mock.ExpectQuery(`SELECT.*string_agg.*FROM "slam_xflow"`).
			WithArgs(1, 2).
			WillReturnRows(detailRows)

		// 执行测试
		bq := SCenterListQuery{
			Developer: "test_dev",
			Key:       "test_key",
		}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_CenterS, 10, 1, bq)

		// 验证结果
		if result == nil {
			t.Fatal("结果不应为空")
		}
		resultList := result.List.([]SCenterSummaryList)
		if len(resultList) != 2 {
			t.Errorf("期望2条记录，实际得到%d条", len(resultList))
		}
		if result.Page.Total != 2 {
			t.Errorf("期望总数为2，实际为%d", result.Page.Total)
		}
		if resultList[0].XflowId != "1" {
			t.Errorf("期望第一条记录XflowId为1，实际为%s", resultList[0].XflowId)
		}
	})

	t.Run("正常查询_无数据", func(t *testing.T) {
		// 设置统计查询期望 - 由于使用了默认条件，会生成复杂的子查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(0)
		mock.ExpectQuery(`SELECT count\(\*\) FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(countRows)

		// 设置ID查询期望
		idRows := sqlmock.NewRows([]string{"id"})
		mock.ExpectQuery(`SELECT DISTINCT sub\.id FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(idRows)

		// 执行测试
		bq := SCenterListQuery{}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_CenterS, 10, 1, bq)

		// 验证结果
		if result == nil {
			t.Fatal("结果不应为空")
		}
		resultList := result.List.([]SCenterSummaryList)
		if len(resultList) != 0 {
			t.Errorf("期望0条记录，实际得到%d条", len(resultList))
		}
		if result.Page.Total != 0 {
			t.Errorf("期望总数为0，实际为%d", result.Page.Total)
		}
	})

	t.Run("统计查询失败", func(t *testing.T) {
		// 设置统计查询失败期望 - 由于使用了默认条件，会生成复杂的子查询
		mock.ExpectQuery(`SELECT count\(\*\) FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnError(fmt.Errorf("数据库连接失败"))

		// 执行测试
		bq := SCenterListQuery{}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_CenterS, 10, 1, bq)

		// 验证结果
		if result != nil {
			t.Error("期望返回nil，实际返回了结果")
		}
	})

	t.Run("ID查询失败", func(t *testing.T) {
		// 设置统计查询期望 - 由于使用了默认条件，会生成复杂的子查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(2)
		mock.ExpectQuery(`SELECT count\(\*\) FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(countRows)

		// 设置ID查询失败期望
		mock.ExpectQuery(`SELECT DISTINCT sub\.id FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnError(fmt.Errorf("查询失败"))

		// 执行测试
		bq := SCenterListQuery{}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_CenterS, 10, 1, bq)

		// 验证结果
		if result != nil {
			t.Error("期望返回nil，实际返回了结果")
		}
	})
}

// TestBuildOptimizedBaseQueryNew 测试构建优化基础查询函数（新版本）
func TestBuildOptimizedBaseQueryNew(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB
	/*
		t.Run("基础查询构建", func(t *testing.T) {
			bq := SCenterListQuery{
				Developer: "test_dev",
				Key:       "test_key",
			}

			// 执行测试
			query := buildOptimizedBaseQuery(global.STOCK_TYPE_CenterS, bq)

			// 验证结果
			if query == nil {
				t.Error("查询对象不应为空")
			}
		})

		t.Run("带排序的查询构建", func(t *testing.T) {
			bq := SCenterListQuery{
				SortColumn: "createtime",
				SortOrder:  "desc",
			}

			// 执行测试
			query := buildOptimizedBaseQuery(global.STOCK_TYPE_CenterS, bq)

			// 验证结果
			if query == nil {
				t.Error("查询对象不应为空")
			}
		})

		t.Run("带搜索条件的查询构建", func(t *testing.T) {
			bq := SCenterListQuery{
				Key:       "search_term",
				Developer: "test_dev",
			}

			// 执行测试
			query := buildOptimizedBaseQuery(global.STOCK_TYPE_CenterS, bq)

			// 验证结果
			if query == nil {
				t.Error("查询对象不应为空")
			}
		})
	*/
}

// TestAddSortJoinsIfNeeded 测试动态添加JOIN函数
// 注释：该函数在当前版本中不存在
/*
func TestAddSortJoinsIfNeeded(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB

	t.Run("creator排序需要JOIN user表", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			SortColumn: "creator",
		}

		// 执行测试
		result := addSortJoinsIfNeeded(query, bq)

		// 验证结果
		if result == nil {
			t.Error("查询对象不应为空")
		}
	})

	t.Run("src_name排序不需要额外JOIN", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			SortColumn: "src_name",
		}

		// 执行测试
		result := addSortJoinsIfNeeded(query, bq)

		// 验证结果
		if result == nil {
			t.Error("查询对象不应为空")
		}
	})

	t.Run("其他排序字段不需要额外JOIN", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			SortColumn: "createtime",
		}

		// 执行测试
		result := addSortJoinsIfNeeded(query, bq)

		// 验证结果
		if result == nil {
			t.Error("查询对象不应为空")
		}
	})
}
*/

// TestApplyConvertOrder 测试ConvertOrder排序函数
func TestApplyConvertOrder(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB
	/*
		t.Run("xflow_id排序不需要GROUP BY", func(t *testing.T) {
			query := db.Table("slam_xflow")
			bq := SCenterListQuery{
				SortColumn: "xflow_id",
				SortOrder:  "desc",
			}

			// 执行测试
			result := applyConvertOrder(query, bq)

			// 验证结果
			if result == nil {
				t.Error("查询对象不应为空")
			}
		})

		t.Run("createtime排序需要GROUP BY", func(t *testing.T) {
			query := db.Table("slam_xflow")
			bq := SCenterListQuery{
				SortColumn: "createtime",
				SortOrder:  "desc",
			}

			// 执行测试
			result := applyConvertOrder(query, bq)

			// 验证结果
			if result == nil {
				t.Error("查询对象不应为空")
			}
		})

		t.Run("src_name排序需要GROUP BY", func(t *testing.T) {
			query := db.Table("slam_xflow")
			bq := SCenterListQuery{
				SortColumn: "src_name",
				SortOrder:  "asc",
			}

			// 执行测试
			result := applyConvertOrder(query, bq)

			// 验证结果
			if result == nil {
				t.Error("查询对象不应为空")
			}
		})

		t.Run("creator排序需要GROUP BY", func(t *testing.T) {
			query := db.Table("slam_xflow")
			bq := SCenterListQuery{
				SortColumn: "creator",
				SortOrder:  "asc",
			}

			// 执行测试
			result := applyConvertOrder(query, bq)

			// 验证结果
			if result == nil {
				t.Error("查询对象不应为空")
			}
		})

		t.Run("空排序字段", func(t *testing.T) {
			query := db.Table("slam_xflow")
			bq := SCenterListQuery{
				SortColumn: "",
				SortOrder:  "desc",
			}

			// 执行测试
			result := applyConvertOrder(query, bq)

			// 验证结果
			if result == nil {
				t.Error("查询对象不应为空")
			}
		})
	*/
}

// TestGetDetailsByXflowIdsNew 测试根据ID获取详细信息函数（新版本）
func TestGetDetailsByXflowIdsNew(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	/*
		mockDB, mock, err := sqlmock.New()
		if err != nil {
			t.Fatalf(mockDBCreateError, err)
		}
		defer mockDB.Close()

		dialector := postgres.New(postgres.Config{
			Conn:       mockDB,
			DriverName: "postgres",
		})

		gormDB, err := gorm.Open(dialector, &gorm.Config{})
		if err != nil {
			t.Fatalf(mockDBOpenError, err)
		}
		db = gormDB

			t.Run("正常获取详细信息", func(t *testing.T) {
				// 设置详细信息查询期望
				detailRows := sqlmock.NewRows([]string{
					"src_name", "xflow_id", "stock_id", "cont_id", "product",
					"koji_id", "current_flow", "stock_type", "user_name",
					"user_id", "xflow_status", "create_time",
				}).
					AddRow("pkg1", "1", "101", "201", "1", "301", "flow1", "type1", "user1", "401", "active", testDate1).
					AddRow("pkg2", "2", "102", "202", "2", "302", "flow2", "type2", "user2", "402", "active", testDate2)
				mock.ExpectQuery(`SELECT.*string_agg.*FROM "slam_xflow"`).
					WithArgs(1, 2).
					WillReturnRows(detailRows)

				// 执行测试
				bq := SCenterListQuery{}
				results := getDetailsByXflowIds([]int{1, 2}, bq)

				// 验证结果
				if len(results) != 2 {
					t.Errorf("期望2条记录，实际得到%d条", len(results))
				}
				if results[0].XflowId != "1" {
					t.Errorf("期望第一条记录XflowId为1，实际为%s", results[0].XflowId)
				}
				if results[1].XflowId != "2" {
					t.Errorf("期望第二条记录XflowId为2，实际为%s", results[1].XflowId)
				}
			})

			t.Run("空ID列表", func(t *testing.T) {
				// 执行测试
				bq := SCenterListQuery{}
				results := getDetailsByXflowIds([]int{}, bq)

				// 验证结果
				if len(results) != 0 {
					t.Errorf("期望0条记录，实际得到%d条", len(results))
				}
			})

			t.Run("查询失败", func(t *testing.T) {
				// 设置查询失败期望
				mock.ExpectQuery(`SELECT.*string_agg.*FROM "slam_xflow"`).
					WithArgs(1).
					WillReturnError(fmt.Errorf("查询失败"))

				// 执行测试
				bq := SCenterListQuery{}
				results := getDetailsByXflowIds([]int{1}, bq)

				// 验证结果
				if len(results) != 0 {
					t.Errorf("期望0条记录，实际得到%d条", len(results))
				}
			})
	*/
}

// TestApplyDistinctWithOrderNew 测试DISTINCT和ORDER BY兼容性处理函数（新版本）
/*
func TestApplyDistinctWithOrderNew(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB

	t.Run("按创建时间排序", func(t *testing.T) {
		query := db.Table("slam_xflow").Select("slam_xflow.id")
		bq := SCenterListQuery{
			SortColumn: "createtime",
			SortOrder:  "desc",
		}

		// 执行测试
		result := applyDistinctWithOrder(query, bq)

		// 验证结果
		if result == nil {
			t.Error("查询对象不应为空")
		}
	})

	t.Run("按ID排序", func(t *testing.T) {
		query := db.Table("slam_xflow").Select("slam_xflow.id")
		bq := SCenterListQuery{
			SortColumn: "xflow_id",
			SortOrder:  "asc",
		}

		// 执行测试
		result := applyDistinctWithOrder(query, bq)

		// 验证结果
		if result == nil {
			t.Error("查询对象不应为空")
		}
	})

	t.Run("按包名排序", func(t *testing.T) {
		query := db.Table("slam_xflow").Select("slam_xflow.id")
		bq := SCenterListQuery{
			SortColumn: "src_name",
			SortOrder:  "desc",
		}

		// 执行测试
		result := applyDistinctWithOrder(query, bq)

		// 验证结果
		if result == nil {
			t.Error("查询对象不应为空")
		}
	})

	t.Run("按创建者排序", func(t *testing.T) {
		query := db.Table("slam_xflow").Select("slam_xflow.id")
		bq := SCenterListQuery{
			SortColumn: "creator",
			SortOrder:  "asc",
		}

		// 执行测试
		result := applyDistinctWithOrder(query, bq)

		// 验证结果
		if result == nil {
			t.Error("查询对象不应为空")
		}
	})

	t.Run("默认排序", func(t *testing.T) {
		query := db.Table("slam_xflow").Select("slam_xflow.id")
		bq := SCenterListQuery{}

		// 执行测试
		result := applyDistinctWithOrder(query, bq)

		// 验证结果
		if result == nil {
			t.Error("查询对象不应为空")
		}
	})
}
*/

// TestOptimizedQueryIntegrationNew 测试优化查询的集成测试（新版本）
func TestOptimizedQueryIntegrationNew(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB

	t.Run("完整的查询流程测试", func(t *testing.T) {
		// 设置统计查询期望 - 由于使用了排序字段，会生成复杂的子查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(1)
		mock.ExpectQuery(`SELECT COUNT\(DISTINCT sub\.id\) FROM \(SELECT DISTINCT sub\.id FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(countRows)

		// 设置ID查询期望
		idRows := sqlmock.NewRows([]string{"id"}).AddRow(1)
		mock.ExpectQuery(`SELECT DISTINCT sub\.id FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(idRows)

		// 设置详细信息查询期望
		detailRows := sqlmock.NewRows([]string{
			"src_name", "xflow_id", "stock_id", "cont_id", "product",
			"koji_id", "current_flow", "stock_type", "user_name",
			"user_id", "xflow_status", "create_time",
		}).AddRow("testpkg", "1", "101", "201", "1", "301", "testflow", "center", "testuser", "401", "active", testDate1)
		mock.ExpectQuery(`SELECT.*string_agg.*FROM "slam_xflow"`).
			WithArgs(1).
			WillReturnRows(detailRows)

		// 执行测试
		bq := SCenterListQuery{
			SortColumn: "createtime",
			SortOrder:  "desc",
		}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_CenterS, 10, 1, bq)

		// 验证结果
		if result == nil {
			t.Fatal("结果不应为空")
		}
		resultList := result.List.([]SCenterSummaryList)
		if len(resultList) != 1 {
			t.Errorf("期望1条记录，实际得到%d条", len(resultList))
		}
		if result.Page.Total != 1 {
			t.Errorf("期望总数为1，实际为%d", result.Page.Total)
		}
		if resultList[0].XflowId != "1" {
			t.Errorf("期望XflowId为1，实际为%s", resultList[0].XflowId)
		}
		if resultList[0].SrcName != "testpkg" {
			t.Errorf("期望SrcName为testpkg，实际为%s", resultList[0].SrcName)
		}
	})
}

// TestBuildBaseQueryWithoutSort 测试构建基础查询（不包含排序）函数
func TestBuildBaseQueryWithoutSort(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB
	/*
		t.Run("基础查询构建_不包含排序", func(t *testing.T) {
			bq := SCenterListQuery{
				Developer: "test_dev",
				Key:       "test_key",
			}

			//执行测试
			query := buildBaseQueryWithoutSort(global.STOCK_TYPE_CenterS, bq)

			//验证结果
			if query == nil {
				t.Error("查询对象不应为空")
			}
		})

		t.Run("带搜索条件的基础查询", func(t *testing.T) {
			bq := SCenterListQuery{
				Key:       "search_term",
				Developer: "test_dev",
			}

			//执行测试
			query := buildBaseQueryWithoutSort(global.STOCK_TYPE_CenterS, bq)

			//验证结果
			if query == nil {
				t.Error("查询对象不应为空")
			}
		})

		t.Run("带类型条件的基础查询", func(t *testing.T) {
			bq := SCenterListQuery{
				CurrentFlow: global.STOCK_FLOW_SUBMIT,
			}

			//执行测试
			query := buildBaseQueryWithoutSort(global.STOCK_TYPE_CenterS, bq)

			// 验证结果
			if query == nil {
				t.Error("查询对象不应为空")
			}
		})

		t.Run("空查询条件", func(t *testing.T) {
			bq := SCenterListQuery{}

			//执行测试
			query := buildBaseQueryWithoutSort(global.STOCK_TYPE_CenterS, bq)

			// 验证结果
			if query == nil {
				t.Error("查询对象不应为空")
			}
		})
	*/
}

// TestOptimizedCountQuery 测试优化后的统计查询性能
func TestOptimizedCountQuery(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, localMock, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB

	t.Run("统计查询不包含排序", func(t *testing.T) {
		// 设置统计查询期望 - 由于使用了排序字段，会生成复杂的子查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(5)
		localMock.ExpectQuery(`SELECT COUNT\(DISTINCT sub\.id\) FROM \(SELECT DISTINCT sub\.id FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(countRows)

		// 设置ID查询期望 - 这个会包含排序
		idRows := sqlmock.NewRows([]string{"id"}).
			AddRow(1).AddRow(2).AddRow(3)
		localMock.ExpectQuery(`SELECT DISTINCT sub\.id FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(idRows)

		// 设置详细信息查询期望
		detailRows := sqlmock.NewRows([]string{
			"src_name", "xflow_id", "stock_id", "cont_id", "product",
			"koji_id", "current_flow", "stock_type", "user_name",
			"user_id", "xflow_status", "create_time",
		}).
			AddRow("pkg1", "1", "101", "201", "1", "301", "flow1", "type1", "user1", "401", "active", testDate1).
			AddRow("pkg2", "2", "102", "202", "2", "302", "flow2", "type2", "user2", "402", "active", testDate2).
			AddRow("pkg3", "3", "103", "203", "3", "303", "flow3", "type3", "user3", "403", "active", testDate1)
		localMock.ExpectQuery(`SELECT.*string_agg.*FROM "slam_xflow"`).
			WithArgs(1, 2, 3).
			WillReturnRows(detailRows)

		// 执行测试
		bq := SCenterListQuery{
			SortColumn: "createtime",
			SortOrder:  "desc",
		}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_CenterS, 10, 1, bq)

		// 验证结果
		if result == nil {
			t.Fatal("结果不应为空")
		}
		if result.Page.Total != 5 {
			t.Errorf("期望总数为5，实际为%d", result.Page.Total)
		}
		resultList := result.List.([]SCenterSummaryList)
		if len(resultList) != 3 {
			t.Errorf("期望3条记录，实际得到%d条", len(resultList))
		}
	})

	t.Run("统计查询性能优化验证", func(t *testing.T) {
		// 验证统计查询使用了优化的子查询结构
		// 由于使用了需要聚合的排序字段，会生成复杂的子查询
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(10)
		localMock.ExpectQuery(`SELECT COUNT\(DISTINCT sub\.id\) FROM \(SELECT DISTINCT sub\.id FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(countRows)

		// ID查询可以包含复杂的排序
		idRows := sqlmock.NewRows([]string{"id"}).AddRow(1)
		localMock.ExpectQuery(`SELECT DISTINCT sub\.id FROM \(SELECT slam_xflow\.id FROM`).
			WillReturnRows(idRows)

		// 详细信息查询
		detailRows := sqlmock.NewRows([]string{
			"src_name", "xflow_id", "stock_id", "cont_id", "product",
			"koji_id", "current_flow", "stock_type", "user_name",
			"user_id", "xflow_status", "create_time",
		}).AddRow("testpkg", "1", "101", "201", "1", "301", "testflow", "center", "testuser", "401", "active", testDate1)
		localMock.ExpectQuery(`SELECT.*string_agg.*FROM "slam_xflow"`).
			WithArgs(1).
			WillReturnRows(detailRows)

		// 执行测试 - 使用需要聚合排序的字段
		bq := SCenterListQuery{
			SortColumn: "src_name", // 这个字段需要聚合排序
			SortOrder:  "desc",
		}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_CenterS, 5, 1, bq)

		// 验证结果
		if result == nil {
			t.Fatal("结果不应为空")
		}
		if result.Page.Total != 10 {
			t.Errorf("期望总数为10，实际为%d", result.Page.Total)
		}
	})
}

// TestGetSCenterSummaryListsOptimizedNew 测试新的优化查询函数
func TestGetSCenterSummaryListsOptimizedNew(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB

	t.Run("正常查询_有数据", func(t *testing.T) {
		// 第一步：设置统计查询期望
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(2)
		mock.ExpectQuery(`SELECT count\(\*\) FROM`).
			WillReturnRows(countRows)

		// 第一步：设置ID查询期望
		idRows := sqlmock.NewRows([]string{"id"}).
			AddRow(1).
			AddRow(2)
		mock.ExpectQuery(`SELECT.*FROM "slam_xflow"`).
			WillReturnRows(idRows)

		// 第二步：设置详细信息查询期望
		detailRows := sqlmock.NewRows([]string{
			"src_name", "xflow_id", "stock_id", "cont_id", "product",
			"koji_id", "current_flow", "stock_type", "user_name",
			"user_id", "xflow_status", "create_time", "evaluators", "evaluate_msg", "platform",
		}).
			AddRow("pkg1", "1", "101", "201", "1", "301", "flow1", "type1", "user1", "401", "active", testDate1, "", "", "").
			AddRow("pkg2", "2", "102", "202", "2", "302", "flow2", "type2", "user2", "402", "active", testDate2, "", "", "")
		mock.ExpectQuery(`SELECT.*string_agg.*FROM "slam_xflow"`).
			WithArgs(1, 2).
			WillReturnRows(detailRows)

		// 执行测试
		bq := SCenterListQuery{
			Developer: "test_dev",
			Key:       "test_key",
		}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_CenterS, 10, 1, bq)

		// 验证结果
		if result == nil {
			t.Fatal("结果不应为空")
		}
		resultList := result.List.([]SCenterSummaryList)
		if len(resultList) != 2 {
			t.Errorf("期望2条记录，实际得到%d条", len(resultList))
		}
		if result.Page.Total != 2 {
			t.Errorf("期望总数为2，实际为%d", result.Page.Total)
		}
		if resultList[0].XflowId != "1" {
			t.Errorf("期望第一条记录XflowId为1，实际为%s", resultList[0].XflowId)
		}
	})

	t.Run("正常查询_无数据", func(t *testing.T) {
		// 第一步：设置统计查询期望
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(0)
		mock.ExpectQuery(`SELECT count\(\*\) FROM`).
			WillReturnRows(countRows)

		// 第一步：设置ID查询期望（空结果）
		idRows := sqlmock.NewRows([]string{"id"})
		mock.ExpectQuery(`SELECT.*FROM "slam_xflow"`).
			WillReturnRows(idRows)

		// 执行测试
		bq := SCenterListQuery{
			Key: "不存在的包",
		}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_CenterS, 10, 1, bq)

		// 验证结果
		if result == nil {
			t.Fatal("结果不应为空")
		}
		resultList := result.List.([]SCenterSummaryList)
		if len(resultList) != 0 {
			t.Errorf("期望0条记录，实际得到%d条", len(resultList))
		}
		if result.Page.Total != 0 {
			t.Errorf("期望总数为0，实际为%d", result.Page.Total)
		}
	})

	t.Run("统计查询失败", func(t *testing.T) {
		// 设置统计查询失败期望
		mock.ExpectQuery(`SELECT count\(\*\) FROM`).
			WillReturnError(fmt.Errorf("数据库连接失败"))

		// 执行测试
		bq := SCenterListQuery{}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_CenterS, 10, 1, bq)

		// 验证结果
		if result != nil {
			t.Error("期望结果为空，但实际不为空")
		}
	})

	t.Run("ID查询失败", func(t *testing.T) {
		// 第一步：设置统计查询期望
		countRows := sqlmock.NewRows([]string{"count"}).AddRow(2)
		mock.ExpectQuery(`SELECT count\(\*\) FROM`).
			WillReturnRows(countRows)

		// 第一步：设置ID查询失败期望
		mock.ExpectQuery(`SELECT.*FROM "slam_xflow"`).
			WillReturnError(fmt.Errorf("ID查询失败"))

		// 执行测试
		bq := SCenterListQuery{}
		result := GetSCenterSummaryListsOptimized(global.STOCK_TYPE_CenterS, 10, 1, bq)

		// 验证结果
		if result != nil {
			t.Error("期望结果为空，但实际不为空")
		}
	})
}

// TestGetOrderedXflowIds 测试获取排序ID列表函数
func TestGetOrderedXflowIds(t *testing.T) {
	// 保存原始数据库连接
	/*
		originalDB := db
		defer func() {
			db = originalDB
		}()

		// 创建模拟数据库
		mockDB, mock, err := sqlmock.New()
		if err != nil {
			t.Fatalf(mockDBCreateError, err)
		}
		defer mockDB.Close()

		dialector := postgres.New(postgres.Config{
			Conn:       mockDB,
			DriverName: "postgres",
		})

		gormDB, err := gorm.Open(dialector, &gorm.Config{})
		if err != nil {
			t.Fatalf(mockDBOpenError, err)
		}
		db = gormDB
			t.Run("正常获取ID列表", func(t *testing.T) {
				// 设置统计查询期望
				countRows := sqlmock.NewRows([]string{"count"}).AddRow(5)
				mock.ExpectQuery(`SELECT count\(\*\) FROM`).
					WillReturnRows(countRows)

				// 设置ID查询期望
				idRows := sqlmock.NewRows([]string{"id"}).
					AddRow(3).AddRow(1).AddRow(5) // 按排序返回的ID
				mock.ExpectQuery(`SELECT.*FROM "slam_xflow"`).
					WillReturnRows(idRows)

				// 执行测试
				bq := SCenterListQuery{
					SortColumn: "createtime",
					SortOrder:  "desc",
				}
				xflowIds, total, err := getOrderedXflowIds(global.STOCK_TYPE_CenterS, 10, 1, bq)

				// 验证结果
				if err != nil {
					t.Errorf("不应该有错误，但得到: %v", err)
				}
				if total != 5 {
					t.Errorf("期望总数为5，实际为%d", total)
				}
				if len(xflowIds) != 3 {
					t.Errorf("期望3个ID，实际得到%d个", len(xflowIds))
				}
				expectedOrder := []int{3, 1, 5}
				for i, id := range xflowIds {
					if id != expectedOrder[i] {
						t.Errorf("位置%d期望ID为%d，实际为%d", i, expectedOrder[i], id)
					}
				}
			})

			t.Run("统计查询失败", func(t *testing.T) {
				// 设置统计查询失败期望
				mock.ExpectQuery(`SELECT count\(\*\) FROM`).
					WillReturnError(fmt.Errorf("统计查询失败"))

				// 执行测试
				bq := SCenterListQuery{}
				xflowIds, total, err := getOrderedXflowIds(global.STOCK_TYPE_CenterS, 10, 1, bq)

				// 验证结果
				if err == nil {
					t.Error("期望有错误，但没有")
				}
				if total != 0 {
					t.Errorf("期望总数为0，实际为%d", total)
				}
				if len(xflowIds) != 0 {
					t.Errorf("期望0个ID，实际得到%d个", len(xflowIds))
				}
			})
	*/
}

// TestGetDetailsByOrderedIds 测试根据ID获取详细信息函数
func TestGetDetailsByOrderedIds(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB

	t.Run("正常获取详细信息", func(t *testing.T) {
		// 设置详细信息查询期望
		detailRows := sqlmock.NewRows([]string{
			"src_name", "xflow_id", "stock_id", "cont_id", "product",
			"koji_id", "current_flow", "stock_type", "user_name",
			"user_id", "xflow_status", "create_time", "evaluators", "evaluate_msg", "platform",
		}).
			AddRow("pkg3", "3", "103", "203", "3", "303", "flow3", "type3", "user3", "403", "active", testDate1, "", "", "").
			AddRow("pkg1", "1", "101", "201", "1", "301", "flow1", "type1", "user1", "401", "active", testDate2, "", "", "")
		mock.ExpectQuery(`SELECT.*string_agg.*FROM "slam_xflow"`).
			WithArgs(3, 1).
			WillReturnRows(detailRows)

		// 执行测试（按顺序传入ID）
		xflowIds := []int{3, 1}
		results := getDetailsByOrderedIds(xflowIds)

		// 验证结果
		if len(results) != 2 {
			t.Errorf("期望2条记录，实际得到%d条", len(results))
		}
		// 验证顺序是否正确
		if results[0].XflowId != "3" {
			t.Errorf("期望第一条记录XflowId为3，实际为%s", results[0].XflowId)
		}
		if results[1].XflowId != "1" {
			t.Errorf("期望第二条记录XflowId为1，实际为%s", results[1].XflowId)
		}
	})

	t.Run("空ID列表", func(t *testing.T) {
		// 执行测试
		results := getDetailsByOrderedIds([]int{})

		// 验证结果
		if len(results) != 0 {
			t.Errorf("期望0条记录，实际得到%d条", len(results))
		}
	})

	t.Run("查询失败", func(t *testing.T) {
		// 设置查询失败期望
		mock.ExpectQuery(`SELECT.*string_agg.*FROM "slam_xflow"`).
			WithArgs(1).
			WillReturnError(fmt.Errorf("查询失败"))

		// 执行测试
		results := getDetailsByOrderedIds([]int{1})

		// 验证结果
		if len(results) != 0 {
			t.Errorf("期望0条记录，实际得到%d条", len(results))
		}
	})
}

// TestGetOrderedXflowIdsOptimized 测试优化的获取排序ID列表函数
func TestGetOrderedXflowIdsOptimized(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB

	t.Run("正常获取排序ID列表", func(t *testing.T) {
		// 模拟查询
		mock.ExpectQuery(regexp.QuoteMeta(`SELECT`)).
			WillReturnRows(sqlmock.NewRows([]string{"id"}).
				AddRow(5).AddRow(3).AddRow(1))

		// 执行测试
		bq := SCenterListQuery{
			SortColumn: "createtime",
			SortOrder:  "descending",
		}
		xflowIds, total, err := getOrderedXflowIdsOptimized("all", 3, 1, bq)

		// 验证结果
		if err != nil {
			t.Errorf("不应该有错误: %v", err)
		}

		expectedIds := []int{5, 3, 1}
		if len(xflowIds) != len(expectedIds) {
			t.Errorf("期望ID数量为%d，实际为%d", len(expectedIds), len(xflowIds))
		}

		for i, expectedId := range expectedIds {
			if xflowIds[i] != expectedId {
				t.Errorf("位置%d期望ID为%d，实际为%d", i, expectedId, xflowIds[i])
			}
		}

		// 验证总数（这里模拟返回的是查询结果的数量）
		if total != 3 {
			t.Errorf("期望总数为3，实际为%d", total)
		}

		// 验证所有期望的查询都被调用
		if err := mock.ExpectationsWereMet(); err != nil {
			t.Errorf("未满足所有模拟期望: %v", err)
		}
	})

	t.Run("查询错误处理", func(t *testing.T) {
		// 模拟查询错误
		mock.ExpectQuery(regexp.QuoteMeta(`SELECT`)).
			WillReturnError(fmt.Errorf("数据库连接失败"))

		// 执行测试
		bq := SCenterListQuery{}
		xflowIds, total, err := getOrderedXflowIdsOptimized("all", 10, 1, bq)

		// 验证结果
		if err == nil {
			t.Error("应该有错误")
		}

		if len(xflowIds) != 0 {
			t.Errorf("期望空ID列表，实际长度为%d", len(xflowIds))
		}

		if total != 0 {
			t.Errorf("期望总数为0，实际为%d", total)
		}

		// 验证所有期望的查询都被调用
		if err := mock.ExpectationsWereMet(); err != nil {
			t.Errorf("未满足所有模拟期望: %v", err)
		}
	})
}

// TestApplySimpleSort 测试简单排序函数
func TestApplySimpleSort(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB

	t.Run("按创建时间降序排序", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			SortColumn: "createtime",
			SortOrder:  "descending",
		}

		// 执行测试
		result := applySimpleSort(query, bq)

		// 验证结果
		if result == nil {
			t.Error("查询对象不应为空")
		}
	})

	t.Run("按ID升序排序", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			SortColumn: "xflow_id",
			SortOrder:  "ascending",
		}

		// 执行测试
		result := applySimpleSort(query, bq)

		// 验证结果
		if result == nil {
			t.Error("查询对象不应为空")
		}
	})

	t.Run("默认排序", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{}

		// 执行测试
		result := applySimpleSort(query, bq)

		// 验证结果
		if result == nil {
			t.Error("查询对象不应为空")
		}
	})
}

// TestApplyDateFilters 测试日期过滤函数
func TestApplyDateFilters(t *testing.T) {
	// 保存原始数据库连接
	originalDB := db
	defer func() {
		db = originalDB
	}()

	// 创建模拟数据库
	mockDB, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf(mockDBCreateError, err)
	}
	defer mockDB.Close()

	dialector := postgres.New(postgres.Config{
		Conn:       mockDB,
		DriverName: "postgres",
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		t.Fatalf(mockDBOpenError, err)
	}
	db = gormDB

	t.Run("应用开始日期过滤", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			StartDate: "2023-01-01",
		}

		// 执行测试
		result := applyDateFilters(query, bq)

		// 验证结果
		if result == nil {
			t.Error("查询对象不应为空")
		}
	})

	t.Run("应用结束日期过滤", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			EndDate: "2023-12-31",
		}

		// 执行测试
		result := applyDateFilters(query, bq)

		// 验证结果
		if result == nil {
			t.Error("查询对象不应为空")
		}
	})

	t.Run("应用日期范围过滤", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{
			StartDate: "2023-01-01",
			EndDate:   "2023-12-31",
		}

		// 执行测试
		result := applyDateFilters(query, bq)

		// 验证结果
		if result == nil {
			t.Error("查询对象不应为空")
		}
	})

	t.Run("无日期过滤", func(t *testing.T) {
		query := db.Table("slam_xflow")
		bq := SCenterListQuery{}

		// 执行测试
		result := applyDateFilters(query, bq)

		// 验证结果
		if result == nil {
			t.Error("查询对象不应为空")
		}
	})
}
