package model

import (
	"errors"
	"fmt"
	"slam/global"
	"slam/slog"
	"strconv"
	"time"

	"gorm.io/gorm"
)

type StockCenterList struct {
	Id              *int      `gorm:"column:id;primaryKey;autoIncrement:true;type:serial4;not null" json:"id"`
	Xflow_id        int       `gorm:"column:xflow_id" json:"xflow_id"`
	Stock_type      string    `gorm:"column:stock_type" json:"stock_type"`
	Packs           []int     `gorm:"column:packs;serializer:json" json:"packs"`
	Supplement_info string    `gorm:"column:supplement_info" json:"supplement_info"`
	Test_info       string    `gorm:"column:test_info" json:"test_info"`
	Appendix        []string  `gorm:"column:appendix;serializer:json" json:"appendix"`
	CreatedAt       time.Time `gorm:"column:create_time" json:"create_time"`
	UpdatedAt       time.Time `gorm:"column:update_time" json:"update_time"`
}

type StockCenterContext struct {
	Id           *int      `gorm:"column:id;primaryKey;autoIncrement:true;type:serial4;not null" json:"id,string,omitempty" label:"序号，主键"`
	Xflow_id     int       `gorm:"column:xflow_id" json:"xflow_id"`
	Current_flow string    `gorm:"column:current_flow" json:"current_flow"`
	Hook_status  string    `gorm:"column:hook_status" json:"hook_status"`
	Hook_id      []string  `gorm:"column:hook_id;serializer:json" json:"hook_id"`
	Review_list  []int     `gorm:"column:review_list;serializer:json" json:"review_list"`
	CreatedAt    time.Time `gorm:"column:create_time" json:"create_time,omitempty" label:"创建时间"`
	UpdatedAt    time.Time `gorm:"column:update_time" json:"update_time,omitempty" label:"更新时间"`
}

type StockCenterPackList struct {
	Id          *int      `gorm:"column:id;primaryKey;autoIncrement:true;type:serial4;not null" json:"id"`
	Xflow_id    int       `gorm:"column:xflow_id" json:"xflow_id"`
	PackName    string    `gorm:"column:pack_name" json:"pack_name"`
	Developer   string    `gorm:"column:developer" json:"developer"`
	ProdRelease string    `gorm:"column:prod_release" json:"prod_release"`
	BizSoftForm string    `gorm:"column:bizsoft_form" json:"bizsoft_form"`
	BizSoftType string    `gorm:"column:bizsoft_type" json:"bizsoft_type"`
	ProdId      int       `gorm:"column:prod_id" json:"prod_id"`
	CmdOption   []string  `gorm:"column:cmd_option;serializer:json" json:"cmd_option"`
	Kojis       []int     `gorm:"column:kojis;serializer:json" json:"kojis"`
	LectoId     int       `gorm:"column:lecto_id" json:"lecto_id"`
	CreatedAt   time.Time `gorm:"column:create_time" json:"create_time,omitempty"`
	UpdatedAt   time.Time `gorm:"column:update_time" json:"update_time,omitempty"`
}

func (StockCenterList) TableName() string {
	return "slam_stockcenterlist"
}

func (StockCenterContext) TableName() string {
	return "slam_stockcentercontext"
}

func (StockCenterPackList) TableName() string {
	return "slam_stcpacklist"
}

type SCenterListQuery struct {
	Developer   string `gorm:"column:developer" json:"developer"`
	ProdRelease string `gorm:"column:prod_release" json:"prod_release"`
	BizSoftForm string `gorm:"column:bizsoft_form" json:"bizsoft_form"`
	BizSoftType string `gorm:"column:bizsoft_type" json:"bizsoft_type"`
	UserId      int    `json:"user_id,omitempty" gorm:"column:user_id"`
	Product     string `json:"prod,omitempty" gorm:"-"`
	StartDate   string `json:"start_date,omitempty" gorm:"-"`
	EndDate     string `json:"end_date,omitempty" gorm:"-"`
	CurrentFlow string `json:"current_flow,omitempty" gorm:"column:current_flow"`
	Key         string `json:"key,omitempty" gorm:"-"`
	SortColumn  string `json:"sort_column,omitempty" gorm:"-"`
	SortOrder   string `json:"sort_order,omitempty" gorm:"-"`
}

type SCenterSummaryList struct {
	SrcName     string `json:"src_name"`
	Product     string `json:"product"`
	CurrentFlow string `json:"current_flow"`
	StockType   string `json:"stock_type"`
	UserName    string `json:"user_name"`
	CreateTime  string `json:"create_time"`
	XflowId     string `json:"xflow_id"`
	StockId     string `json:"stock_id"`
	ContId      string `json:"cont_id"`
	UserId      string `json:"user_id"`
	KojiId      string `json:"koji_id"`
	Evaluators  string `json:"evaluators"`
	EvaluateMsg string `json:"evaluate_msg"`
	XflowStatus string `json:"xflow_status"`
	Platform    string `json:"platform"`
}

// 当前软件包所有处于入库流程中的信息（流程为active或finished的）
type SCenterVerifyList struct {
	ProdId    int      `json:"prod_id"`
	PackName  string   `json:"pack_name" label:"处于入库流程中的软件包名"`
	KojiIp    string   `json:"koji_ip"`
	KojiId    int      `json:"koji_id"`
	CmdOption []string `gorm:"column:cmd_option;serializer:json" json:"cmd_option"`
}

// 搜索软件包时信息查询结果
type SCPacksSearchRes struct {
	PackName string `json:"pack_name" label:"软件包名"`
	XflowId  int    `json:"xflow_id" label:"流程ID"`
}

var QueryXflowId = "xflow_id = ?"
var NotExist = "not exist"

// 数据库字段常量
const SlamXflowIdField = "slam_xflow.id"

func GetStockCenterListByXflowId(xflowId int) *StockCenterList {
	var stockCenterList StockCenterList
	err := db.Where(QueryXflowId, xflowId).First(&stockCenterList).Error
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			slog.Error.Println("GetStockCenterListByXflowId failed:", xflowId, err)
		}
		return nil
	}
	return &stockCenterList
}

func CreateStockCenterList(stockCenterList *StockCenterList) bool {
	err := db.Create(&stockCenterList).Error
	if err != nil {
		slog.Error.Println("Create StockCenterList failed:", err)
		return false
	}
	return true
}

func CreateStockCenterListBatch(stockCenterList *[]StockCenterList) bool {
	err := db.CreateInBatches(stockCenterList, 10).Error
	if err != nil {
		slog.Error.Println("CreateStockCenterListBatch failed:", err)
		return false
	}
	return true
}

func UpdateStockCenterList(stockCenterList *StockCenterList) bool {
	stockcenterlistTmp := GetStockCenterListByXflowId(stockCenterList.Xflow_id)
	if stockcenterlistTmp == nil {
		slog.Error.Println("UpdateStockCenterList failed, Xflow_id", stockCenterList.Xflow_id, NotExist)
		return false
	}

	err := db.Updates(stockCenterList).Error
	if err != nil {
		slog.Error.Println("Update StockCenterList failed:", err)
		return false
	}
	return true
}

func GetStockCenterContextByXflowId(xflowId int) *StockCenterContext {
	var stockCenterContext StockCenterContext
	err := db.Where(QueryXflowId, xflowId).First(&stockCenterContext).Error
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			slog.Error.Println("GetStockCenterContextByXflowId failed:", xflowId, err)
		}
		return nil
	}
	return &stockCenterContext
}

func CreateStockCenterContext(stockCenterContext *StockCenterContext) bool {
	err := db.Create(&stockCenterContext).Error
	if err != nil {
		slog.Error.Println("Create StockCenterContext failed:", err)
		return false
	}
	return true
}

func CreateStockCenterContextBatch(stockCenterContext *[]StockCenterContext) bool {
	err := db.CreateInBatches(stockCenterContext, 10).Error
	if err != nil {
		slog.Error.Println("CreateStockCenterContextBatch failed:", err)
		return false
	}
	return true
}

func UpdateStockCenterContext(stockCenterContext *StockCenterContext) bool {
	stockcentercontextTmp := GetStockCenterContextByXflowId(stockCenterContext.Xflow_id)
	if stockcentercontextTmp == nil {
		slog.Error.Println("UpdateStockCenterContext failed, Xflow_id", stockCenterContext.Xflow_id, NotExist)
		return false
	}

	//内存设置状态信息
	SafeSetCurrentFlow(stockCenterContext.Xflow_id, stockCenterContext.Current_flow)

	err := db.Updates(stockCenterContext).Error
	if err != nil {
		slog.Error.Println("Update StockCenterContext failed:", err)
		return false
	}
	return true
}

func GetStcListById(id int) *StockCenterPackList {
	var stockCenterPackList StockCenterPackList
	err := db.Where("id = ?", id).First(&stockCenterPackList).Error
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			slog.Error.Println("GetStcListById failed:", id, err)
		}
		return nil
	}
	return &stockCenterPackList
}

func GetStcListByXflowId(xflowId int) []StockCenterPackList {
	var stockCenterPackLists []StockCenterPackList
	err := db.Order("id asc").Where(QueryXflowId, xflowId).Find(&stockCenterPackLists).Error
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			slog.Error.Println("GetStcListByXflowId failed:", xflowId, err)
		}
		return nil
	}
	return stockCenterPackLists
}

func CreateStcPackList(stockCenterPackList *StockCenterPackList) bool {
	err := db.Create(&stockCenterPackList).Error
	if err != nil {
		slog.Error.Println("Create StockCenterPackList failed:", err)
		return false
	}
	return true
}

// 批量插入包数据
func CreateStcPackListBacth(stockCenterPackList *[]StockCenterPackList) error {
	err := db.CreateInBatches(stockCenterPackList, 10).Error
	if err != nil {
		slog.Error.Println("CreateStcPackListBacth failed:", err)
	}
	return err
}

func UpdateStcPackList(stockCenterPackList *StockCenterPackList) bool {
	stockcenterpacklistTmp := GetStcListById(*stockCenterPackList.Id)
	if stockcenterpacklistTmp == nil {
		slog.Error.Println("UpdateStcPackList failed,id", *stockCenterPackList.Id, NotExist)
		return false
	}

	err := db.Updates(stockCenterPackList).Error
	if err != nil {
		slog.Error.Println("Update StockCenterPackList failed:", err)
		return false
	}
	return true
}

// 判断某源码包是否在中心仓入库里处于流程中
func CheckCPackListByPackNameOrigin(packName string, kojiPlatIds []string) (bool, []SCenterVerifyList) {
	var scenterVerifyLists []SCenterVerifyList
	ret := db.Table("slam_stcpacklist").
		Select("slam_kojilist.src_name as pack_name", "slam_taglist.prod_id as prod_id",
			"slam_kojilist.ip as koji_ip", "slam_stcpacklist.cmd_option as cmd_option",
			"slam_koji_platform.id as koji_id").
		Joins("left join slam_xflow on slam_stcpacklist.xflow_id = "+SlamXflowIdField).
		Joins("left join slam_kojilist on slam_kojilist.xflow_id = "+SlamXflowIdField).
		Joins("left join slam_taglist on slam_taglist.id = slam_kojilist.tag").
		Joins("left join slam_koji_platform on slam_koji_platform.Addr = slam_kojilist.ip").
		Where("slam_kojilist.ip in (?)", kojiPlatIds).
		Where("slam_xflow.status in (?)", []string{global.XFLOW_STATUS_ACTIVE, global.XFLOW_STATUS_FINISHED}).
		Where("slam_kojilist.type in (?)", []string{global.KOJI_TYPE_BUILD, global.KOJI_TYPE_TASK}).
		Where("slam_stcpacklist.pack_name = ?", packName).
		Find(&scenterVerifyLists)

	if ret.Error != nil {
		slog.Error.Println("CheckCPackListByPackNameOrigin failed:", ret.Error)
		return false, []SCenterVerifyList{}
	}
	return true, scenterVerifyLists
}

func FindStockCenterPackInXflow(packName string) bool {
	var total int64

	ret := db.Table("slam_xflow").
		Joins("left join StockCenterContext on StockCenterContext.xflow_id = "+SlamXflowIdField).
		Where("StockCenterContext.pack_name = ?", packName).
		Where("slam_xflow.status = ? OR slam_xflow.status = ?", global.XFLOW_STATUS_ACTIVE, global.XFLOW_STATUS_FINISHED).
		Count(&total)

	if ret.Error != nil {
		if !errors.Is(ret.Error, gorm.ErrRecordNotFound) {
			slog.Error.Println("FindStockCenterPackInXflow failed:", ret.Error)
		}
		return false
	}

	if total == 0 {
		return false
	}
	return true
}

// 查找指定发行商、发行版本的，且已在入库流程中的软件包数据
func SearchSCPacksBaseDev(developers, prodRelease, bizsoftForm, bizsoftType, packname string) (bool, []SCPacksSearchRes) {
	infos := []SCPacksSearchRes{}
	ret := db.Table("slam_stcpacklist").
		Select("slam_stcpacklist.pack_name as pack_name,"+
			"slam_stcpacklist.xflow_id as xflow_id").
		Joins("left join slam_xflow on slam_stcpacklist.xflow_id = "+SlamXflowIdField).
		Where("slam_stcpacklist.developer = ?", developers).
		Where("slam_stcpacklist.prod_release = ?", prodRelease).
		Where("slam_stcpacklist.bizsoft_form = ?", bizsoftForm).
		Where("slam_stcpacklist.bizsoft_type = ?", bizsoftType).
		Where("slam_xflow.status in (?)", []string{global.XFLOW_STATUS_ACTIVE, global.XFLOW_STATUS_FINISHED})
	if len(packname) > 0 {
		ret = ret.Where("slam_stcpacklist.pack_name ilike ?", "%"+packname+"%")
	}
	ret = ret.Find(&infos)
	if ret.Error != nil {
		if !errors.Is(ret.Error, gorm.ErrRecordNotFound) {
			slog.Error.Println("SearchSCPacksBaseDev failed:", ret.Error)
		}
		return false, infos
	}
	return true, infos
}

// GetSCenterSummaryLists  优化后的查询列表函数
func GetSCenterSummaryLists(t string, pageSize, pageIndex int, bq SCenterListQuery) *Data_List {
	// 使用优化后的查询方法
	return GetSCenterSummaryListsOptimized(t, pageSize, pageIndex, bq)
}

// 优化后的查询函数 - 拆分复杂查询为多个简单查询
// 参数: t - 库存类型, pageSize - 页大小, pageIndex - 页索引, bq - 查询条件
// 返回: *Data_List - 查询结果列表
func GetSCenterSummaryListsOptimized(t string, pageSize, pageIndex int, bq SCenterListQuery) *Data_List {
	// 第一步：获取排序后的xflow_id列表（轻量级查询）
	xflowIds, total, err := getOrderedXflowIdsOptimized(t, pageSize, pageIndex, bq)
	if err != nil {
		slog.Error.Printf("GetSCenterSummaryListsOptimized 获取ID列表失败 - 类型: %s, 错误: %v", t, err)
		return nil
	}

	if len(xflowIds) == 0 {
		return &Data_List{List: []SCenterSummaryList{}, Page: PageRes{pageIndex, pageSize, total}}
	}

	// 第二步：根据ID列表获取详细信息（保持排序）
	infos := getDetailsByOrderedIds(xflowIds)

	return &Data_List{List: infos, Page: PageRes{pageIndex, pageSize, total}}
}

// 应用搜索条件
// 参数: query - GORM查询对象, bq - 查询条件对象
// 返回: *gorm.DB - 应用搜索条件后的查询对象
func applySearchConditions(query *gorm.DB, bq SCenterListQuery) *gorm.DB {
	if bq.Key != "" {
		query = query.Where("slam_stcpacklist.pack_name ILIKE ?", "%"+bq.Key+"%")
	}
	return query
}

// 应用库存类型相关条件
// 参数: query - GORM查询对象, t - 库存类型, bq - 查询条件对象
// 返回: *gorm.DB - 应用类型条件后的查询对象
func applyStockTypeConditions(query *gorm.DB, t string, bq SCenterListQuery) *gorm.DB {
	if t == global.STOCK_TYPE_BUSINESS {
		return applyBusinessTypeConditions(query, bq)
	}
	return applyCenterTypeConditions(query, bq)
}

// 应用业务类型条件
// 参数: query - GORM查询对象, bq - 查询条件对象
// 返回: *gorm.DB - 应用业务类型条件后的查询对象
func applyBusinessTypeConditions(query *gorm.DB, bq SCenterListQuery) *gorm.DB {
	query = query.Where("slam_stcpacklist.bizsoft_form != ''")
	if bq.BizSoftForm != "" {
		query = query.Where("slam_stcpacklist.bizsoft_form = ?", bq.BizSoftForm)
	}
	if bq.BizSoftType != "" {
		query = query.Where("slam_stcpacklist.bizsoft_type = ?", bq.BizSoftType)
	}
	return query
}

// 应用中心仓类型条件
// 参数: query - GORM查询对象, bq - 查询条件对象
// 返回: *gorm.DB - 应用中心仓类型条件后的查询对象
func applyCenterTypeConditions(query *gorm.DB, bq SCenterListQuery) *gorm.DB {
	query = query.Where("slam_stcpacklist.developer != ''")
	if bq.Developer != "" {
		query = query.Where("slam_stcpacklist.developer = ?", bq.Developer)
	}
	if bq.ProdRelease != "" {
		query = query.Where("slam_stcpacklist.prod_release = ?", bq.ProdRelease)
	}
	return query
}

// 应用日期过滤条件
// 参数: query - GORM查询对象, bq - 查询条件对象
// 返回: *gorm.DB - 应用日期过滤条件后的查询对象
func applyDateFilters(query *gorm.DB, bq SCenterListQuery) *gorm.DB {
	if bq.StartDate != "" {
		query = query.Where("slam_xflow.create_time >= ?", bq.StartDate)
	}
	if bq.EndDate != "" {
		query = query.Where("slam_xflow.create_time <= ?", bq.EndDate)
	}
	return query
}
func GetStockType(xflowId int) string {
	var stockType string
	err := db.Debug().Model(StockCenterList{}).Where("xflow_id=?", xflowId).Pluck("stock_type", &stockType).Error
	if err != nil {
		slog.Error.Println(err)
		return ""
	}
	return stockType
}

// 获取排序后的xflow_id列表（第一步优化查询）
// 参数: t - 库存类型, pageSize - 页大小, pageIndex - 页索引, bq - 查询条件
// 返回: xflowIds - ID列表, total - 总数, err - 错误
/*
func getOrderedXflowIds(t string, pageSize, pageIndex int, bq SCenterListQuery) ([]int, int64, error) {
	// 构建轻量级查询，只获取必要的字段用于排序
	query := buildSortQuery(t, bq)

	// 获取总数
	var total int64
	countQuery := buildSimpleCountQuery(t, bq)
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("统计查询失败: %v", err)
	}

	// 获取排序后的ID列表
	var xflowIds []int
	if err := query.Limit(pageSize).Offset((pageIndex-1)*pageSize).Pluck(SlamXflowIdField, &xflowIds).Error; err != nil {
		return nil, 0, fmt.Errorf("ID查询失败: %v", err)
	}

	return xflowIds, total, nil
}
// 构建排序查询 - 只包含排序必需的字段和JOIN
// 参数: t - 库存类型, bq - 查询条件对象
// 返回: *gorm.DB - 构建好的排序查询对象
func buildSortQuery(t string, bq SCenterListQuery) *gorm.DB {
	// 基础查询
	query := db.Table("slam_xflow").
		Select(SlamXflowIdField).
		Where("slam_xflow.type IN (?)", []string{global.XFLOW_TYPE_STOCK_CENTER, global.XFLOW_TYPE_STOCK_BIZ})

	// 根据排序字段动态添加必要的JOIN
	query = addSortJoinsForQuery(query, bq)

	// 应用搜索条件
	query = applySearchConditions(query, bq)

	// 应用类型相关条件
	query = applyStockTypeConditions(query, t, bq)

	// 应用通用过滤条件
	query = applyCommonFiltersForSort(query, bq)

	// 应用排序（使用优化的排序逻辑）
	query = applySortWithOptimization(query, bq)

	return query
}
*/
// 构建简单统计查询
// 参数: t - 库存类型, bq - 查询条件对象
// buildSimpleCountQuery 函数用于构建统计查询对象。
// 返回: *gorm.DB - 构建好的统计查询对象
func buildSimpleCountQuery(t string, bq SCenterListQuery) *gorm.DB {
	query := db.Table("slam_xflow").
		Select("DISTINCT "+SlamXflowIdField).
		Joins("LEFT JOIN slam_stcpacklist ON slam_stcpacklist.xflow_id = "+SlamXflowIdField).
		Where("slam_xflow.type IN (?)", []string{global.XFLOW_TYPE_STOCK_CENTER, global.XFLOW_TYPE_STOCK_BIZ})

	// 应用搜索条件
	query = applySearchConditions(query, bq)

	// 应用类型相关条件
	query = applyStockTypeConditions(query, t, bq)

	// 应用通用过滤条件（简化版）
	query = applyCommonFiltersForSort(query, bq)

	return query
}

// 应用优化的排序逻辑
// 参数: query - GORM查询对象, bq - 查询条件对象
// 返回: *gorm.DB - 应用排序后的查询对象
func applySortWithOptimization(query *gorm.DB, bq SCenterListQuery) *gorm.DB {
	// 对于需要聚合的排序字段，使用GROUP BY
	needsGroupBy := (bq.SortColumn == "src_name" || bq.SortColumn == "creator")
	if needsGroupBy {
		query = query.Group(SlamXflowIdField)
	}

	// 应用排序
	return applySimpleSort(query, bq)
}

// 应用通用过滤条件（用于排序查询）
// 参数: query - GORM查询对象, bq - 查询条件对象
// 返回: *gorm.DB - 应用过滤条件后的查询对象
func applyCommonFiltersForSort(query *gorm.DB, bq SCenterListQuery) *gorm.DB {
	// 产品过滤
	if bq.Product != "" {
		query = query.Joins("LEFT JOIN slam_products ON slam_products.id = slam_stcpacklist.prod_id").
			Where("slam_products.id = ?", bq.Product)
	}

	// 时间范围过滤
	query = applyDateFilters(query, bq)

	// 流程状态过滤
	if bq.CurrentFlow != "" {
		query = query.Joins("LEFT JOIN slam_stockcentercontext ON slam_stockcentercontext.xflow_id = " + SlamXflowIdField)
		if bq.CurrentFlow == global.STOCK_FLOW_FAILED {
			query = query.Where("slam_xflow.status IN (?)", []string{global.XFLOW_STATUS_FAILED, global.XFLOW_STATUS_REVOKE})
		} else if bq.CurrentFlow == global.STOCK_FLOW_SUBMIT {
			query = query.Where("slam_stockcentercontext.current_flow IN (?)",
				[]string{global.STOCK_CENTER_FLOW_SUBMIT, global.STOCK_CENTER_FLOW_DOWNLOAD, global.STOCK_CENTER_FLOW_SCRATCH_BUILD})
		} else {
			query = query.Where("slam_stockcentercontext.current_flow = ?", bq.CurrentFlow)
		}
	}

	// 用户过滤
	if bq.UserId > 0 {
		query = query.Where("slam_xflow.user_id = ?", bq.UserId)
	}

	return query
}

// 根据排序后的ID列表获取详细信息（第二步优化查询）
// 参数: xflowIds - 排序后的ID列表
// 返回: []SCenterSummaryList - 详细信息列表
func getDetailsByOrderedIds(xflowIds []int) []SCenterSummaryList {
	if len(xflowIds) == 0 {
		return []SCenterSummaryList{}
	}

	// 使用批量查询获取详细信息
	var results []SCenterSummaryList
	query := db.Table("slam_xflow").
		Select(`
			string_agg(DISTINCT slam_stcpacklist.pack_name, ',') as src_name,
			`+SlamXflowIdField+`::text as xflow_id,
			string_agg(DISTINCT slam_stockcenterlist.id::text, ',') as stock_id,
			string_agg(DISTINCT slam_stockcentercontext.id::text, ',') as cont_id,
			string_agg(DISTINCT slam_products.id::text, ',') as product,
			string_agg(DISTINCT slam_kojilist.id::text, ',') as koji_id,
			string_agg(DISTINCT slam_stockcentercontext.current_flow, ',') as current_flow,
			string_agg(DISTINCT slam_stockcenterlist.stock_type, ',') as stock_type,
			string_agg(DISTINCT "user".nickname, ',') as user_name,
			string_agg(DISTINCT slam_xflow.user_id::text, ',') as user_id,
			string_agg(DISTINCT slam_xflow.status, ',') as xflow_status,
			string_agg(DISTINCT slam_xflow.create_time::text, ',') as create_time,
			string_agg(DISTINCT '', ',') as evaluators,
			string_agg(DISTINCT '', ',') as evaluate_msg,
			string_agg(DISTINCT '', ',') as platform
		`).
		Joins("LEFT JOIN slam_stockcentercontext ON slam_stockcentercontext.xflow_id = "+SlamXflowIdField).
		Joins("LEFT JOIN slam_stockcenterlist ON slam_stockcenterlist.xflow_id = "+SlamXflowIdField).
		Joins("LEFT JOIN slam_stcpacklist ON slam_stcpacklist.xflow_id = "+SlamXflowIdField).
		Joins("LEFT JOIN slam_kojilist ON slam_kojilist.xflow_id = "+SlamXflowIdField).
		Joins("LEFT JOIN \"user\" ON \"user\".id = slam_xflow.user_id").
		Joins("LEFT JOIN slam_products ON slam_products.id = slam_stcpacklist.prod_id").
		Where(SlamXflowIdField+" IN (?)", xflowIds).
		Group(SlamXflowIdField)

	if err := query.Find(&results).Error; err != nil {
		slog.Error.Printf("getDetailsByOrderedIds 查询失败: %v", err)
		return []SCenterSummaryList{}
	}

	// 按照输入的xflowIds顺序重新排列结果
	return maintainXflowIdOrder(results, xflowIds)
}

// 按照指定的xflowIds顺序重新排列查询结果
// 参数: results - 查询结果列表, xflowIds - 期望的ID顺序
// 返回: []SCenterSummaryList - 按顺序排列的结果列表
func maintainXflowIdOrder(results []SCenterSummaryList, xflowIds []int) []SCenterSummaryList {
	// 创建结果映射，key为xflow_id，value为对应的记录
	resultMap := make(map[int]SCenterSummaryList)
	for _, result := range results {
		// 将xflow_id字符串转换为整数
		if xflowId, err := strconv.Atoi(result.XflowId); err == nil {
			resultMap[xflowId] = result
		} else {
			slog.Error.Printf("maintainXflowIdOrder: 无法转换xflow_id '%s' 为整数: %v", result.XflowId, err)
		}
	}

	// 按照xflowIds的顺序构建最终结果
	orderedResults := make([]SCenterSummaryList, 0, len(xflowIds))
	for _, xflowId := range xflowIds {
		if result, exists := resultMap[xflowId]; exists {
			orderedResults = append(orderedResults, result)
		} else {
			slog.Warn.Printf("maintainXflowIdOrder: 未找到xflow_id %d 对应的详细信息", xflowId)
		}
	}

	return orderedResults
}

// 应用简单排序逻辑
// 参数: query - GORM查询对象, bq - 查询条件对象
// 返回: *gorm.DB - 应用排序后的查询对象
func applySimpleSort(query *gorm.DB, bq SCenterListQuery) *gorm.DB {
	if bq.SortColumn == "" {
		// 默认按ID降序排序
		return query.Order(SlamXflowIdField + " DESC")
	}

	// 构建排序字段和方向
	sortOrder := "DESC"
	if bq.SortOrder == "ascending" {
		sortOrder = "ASC"
	}

	// 根据排序字段处理GROUP BY和聚合函数
	switch bq.SortColumn {
	case "createtime":
		// 时间字段可以直接排序
		return query.Order("slam_xflow.create_time " + sortOrder)
	case "src_name":
		// 包名需要使用聚合函数，因为可能有多个包
		return query.Order("MIN(slam_stcpacklist.pack_name) " + sortOrder)
	case "creator":
		// 用户昵称需要使用聚合函数
		return query.Order("MIN(\"user\".nickname) " + sortOrder)
	case "xflow_id", "id":
		// ID字段可以直接排序
		return query.Order(SlamXflowIdField + " " + sortOrder)
	default:
		// 默认按ID排序
		return query.Order(SlamXflowIdField + " " + sortOrder)
	}
}

// 获取排序后的xflow_id列表（第一步优化查询）
// 参数: t - 库存类型, pageSize - 页大小, pageIndex - 页索引, bq - 查询条件
// 返回: xflowIds - ID列表, total - 总数, err - 错误

/*
func getOrderedXflowIds(t string, pageSize, pageIndex int, bq SCenterListQuery) ([]int, int64, error) {
	// 方案1：使用两个独立的查询（推荐）
	// 优点：统计查询简单高效，排序查询功能完整
	// 缺点：需要两次数据库查询

	// 获取总数 - 使用简化的统计查询（不包含排序和GROUP BY）
	var total int64
	countQuery := buildSimpleCountQuery(t, bq)
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("统计查询失败: %v", err)
	}

	// 获取排序后的ID列表 - 使用完整的排序查询
	var xflowIds []int
	sortQuery := buildSortQuery(t, bq)
	if err := sortQuery.Limit(pageSize).Offset((pageIndex-1)*pageSize).Pluck(SlamXflowIdField, &xflowIds).Error; err != nil {
		return nil, 0, fmt.Errorf("ID查询失败: %v", err)
	}

	return xflowIds, total, nil
}
*/
// 根据排序需求动态添加必要的JOIN
// 参数: query - GORM查询对象, bq - 查询条件对象
// 返回: *gorm.DB - 添加JOIN后的查询对象
func addSortJoinsForQuery(query *gorm.DB, bq SCenterListQuery) *gorm.DB {
	switch bq.SortColumn {
	case "creator":
		// 需要JOIN user表来获取用户昵称
		query = query.Joins("LEFT JOIN \"user\" ON \"user\".id = slam_xflow.user_id")
	case "src_name":
		// slam_stcpacklist 表已经在基础查询中JOIN了，不需要额外处理
		// 但需要注意：如果一个xflow有多个包，排序可能不稳定
		// 这里我们使用第一个包名进行排序
	}
	return query
}

// 获取排序后的xflow_id列表（优化版本 - 单查询方案）
// 这是一个可选的优化方案，可以减少数据库查询次数
func getOrderedXflowIdsOptimized(t string, pageSize, pageIndex int, bq SCenterListQuery) ([]int, int64, error) {
	// 方案2：使用窗口函数的单查询方案（可选）
	// 优点：只需要一次数据库查询
	// 缺点：查询复杂度较高，可能影响性能

	// 构建带有总数统计的查询
	query := db.Table("slam_xflow").
		Select(SlamXflowIdField+", COUNT(*) OVER() as total_count").
		Joins("LEFT JOIN slam_stcpacklist ON slam_stcpacklist.xflow_id = "+SlamXflowIdField).
		Where("slam_xflow.type IN (?)", []string{global.XFLOW_TYPE_STOCK_CENTER, global.XFLOW_TYPE_STOCK_BIZ})

	// 根据排序字段动态添加必要的JOIN
	query = addSortJoinsForQuery(query, bq)

	// 应用搜索条件
	query = applySearchConditions(query, bq)

	// 应用类型相关条件
	query = applyStockTypeConditions(query, t, bq)

	// 应用通用过滤条件
	query = applyCommonFiltersForSort(query, bq)

	// 应用排序（使用优化的排序逻辑）
	query = applySortWithOptimization(query, bq)

	// 执行查询
	type Result struct {
		ID         int   `gorm:"column:id"`
		TotalCount int64 `gorm:"column:total_count"`
	}

	var results []Result
	if err := query.Limit(pageSize).Offset((pageIndex - 1) * pageSize).Find(&results).Error; err != nil {
		return nil, 0, fmt.Errorf("查询失败: %v", err)
	}

	if len(results) == 0 {
		return []int{}, 0, nil
	}

	// 提取ID列表和总数
	xflowIds := make([]int, len(results))
	var total int64
	for i, result := range results {
		xflowIds[i] = result.ID
		total = result.TotalCount // 所有行的total_count都相同
	}

	return xflowIds, total, nil
}
